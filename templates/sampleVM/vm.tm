import 'package:injectable/injectable.dart';

import 'package:mobx/mobx.dart';

part '{name}[-s]_vm.g.dart';

enum {name}[-C]Event {
  init
}

@injectable
class {name}[-C]VM = _{name}[-C]VM with _${name}[-C]VM;

abstract class _{name}[-C]VM with Store {

  @observable
  {name}[-C]Event? event;

  _{name}[-C]VM() {
        init();
  }

  init() async {}

  @action
      clearEvent() {
        event = null;
  }
}
