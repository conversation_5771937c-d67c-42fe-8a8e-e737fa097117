# 双因素认证 (2FA) 功能使用指南

## 功能概述

本密码管理应用现已支持双因素认证 (2FA) 功能，可以为您的账号添加额外的安全层。2FA 使用基于时间的一次性密码 (TOTP) 算法，与 Google Authenticator、Microsoft Authenticator 等主流认证器应用兼容。

## 主要特性

### 1. 2FA 密钥管理
- **自动生成密钥**：系统可以自动生成符合标准的 Base32 编码密钥
- **手动输入密钥**：支持手动输入从其他服务获取的 2FA 密钥
- **密钥验证**：自动验证密钥格式的正确性
- **安全存储**：2FA 密钥与密码一样进行加密存储

### 2. 验证码生成与显示
- **实时生成**：基于当前时间生成 6 位数字验证码
- **倒计时显示**：显示验证码剩余有效时间
- **自动刷新**：验证码每 30 秒自动更新
- **手动刷新**：支持手动刷新验证码
- **一键复制**：点击即可复制验证码到剪贴板

### 3. 用户界面集成
- **账号列表标识**：在账号列表中显示 2FA 状态图标
- **详情页展示**：在账号详情页面专门展示 2FA 验证码
- **编辑页配置**：在添加/编辑账号时可配置 2FA 设置

## 使用方法

### 添加 2FA 到新账号

1. **创建账号**
   - 点击右上角的"+"按钮添加新账号
   - 填写基本信息（账号名称、用户名、密码等）

2. **配置 2FA**
   - 在"安全设置"部分找到"双因素认证 (2FA)"选项
   - 点击进入 2FA 配置页面
   - 开启"启用2FA"开关

3. **设置密钥**
   - **自动生成**：点击"生成密钥"按钮自动生成新密钥
   - **手动输入**：在密钥输入框中输入已有的 2FA 密钥
   - **从剪贴板粘贴**：点击粘贴按钮从剪贴板导入密钥

4. **保存配置**
   - 确认密钥正确后点击"确定"
   - 保存账号信息

### 为现有账号添加 2FA

1. **打开账号详情**
   - 在账号列表中点击要配置的账号
   - 进入账号详情页面

2. **编辑账号**
   - 点击右上角菜单选择"编辑"
   - 进入账号编辑页面

3. **配置 2FA**
   - 按照上述"配置 2FA"步骤进行设置
   - 保存更改

### 查看和使用 2FA 验证码

1. **识别启用 2FA 的账号**
   - 在账号列表中，启用了 2FA 的账号会显示小锁图标

2. **查看验证码**
   - 点击账号进入详情页面
   - 在"双因素认证 (2FA)"部分查看当前验证码
   - 验证码每 30 秒自动更新

3. **使用验证码**
   - 点击复制按钮将验证码复制到剪贴板
   - 在需要的地方粘贴使用
   - 注意验证码的有效时间（右侧圆形进度条显示）

## 技术实现

### 核心组件

1. **TwoFactorService**
   - 负责 2FA 密钥生成、验证码生成和验证
   - 支持标准 TOTP 算法 (RFC 6238)
   - 兼容主流认证器应用

2. **AccountModel 扩展**
   - 添加 `twoFactorEnabled` 字段标识是否启用 2FA
   - 添加 `twoFactorSecret` 字段存储加密的 2FA 密钥

3. **UI 组件**
   - `TwoFactorConfigBottomSheet`：2FA 配置弹窗
   - 账号详情页面的 2FA 验证码显示区域
   - 账号列表项的 2FA 状态指示器

### 安全特性

1. **加密存储**
   - 2FA 密钥使用与密码相同的加密方式存储
   - 密钥在内存中的存在时间最小化

2. **输入验证**
   - 自动验证 2FA 密钥格式
   - 清理用户输入的特殊字符和空格

3. **时间同步**
   - 基于系统时间生成验证码
   - 支持时间窗口容错（±30秒）

## 兼容性

### 支持的认证器应用
- Google Authenticator
- Microsoft Authenticator
- Authy
- 1Password
- Bitwarden
- 其他支持 TOTP 标准的认证器应用

### 导入导出
- 支持生成标准的 `otpauth://` URI
- 可通过二维码或文本方式导入到认证器应用
- 支持从其他密码管理器导入 2FA 密钥

## 注意事项

1. **备份重要性**
   - 请妥善保管 2FA 密钥的备份
   - 建议在多个认证器应用中添加相同密钥

2. **时间同步**
   - 确保设备时间准确，时间偏差可能导致验证码无效
   - 验证码有 30 秒有效期

3. **安全建议**
   - 不要在不安全的环境中显示或输入 2FA 密钥
   - 定期检查和更新 2FA 配置

## 故障排除

### 验证码无效
1. 检查设备时间是否准确
2. 确认密钥输入正确
3. 等待下一个 30 秒周期重试

### 无法生成验证码
1. 检查账号是否正确启用 2FA
2. 验证密钥格式是否正确
3. 重新配置 2FA 设置

### 密钥丢失
1. 如果有备份，重新输入密钥
2. 如果没有备份，需要在原服务中重新设置 2FA
3. 更新应用中的 2FA 配置
