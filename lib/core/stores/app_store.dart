import 'dart:async';
import 'package:flutter/material.dart';
import 'package:mobx/mobx.dart';
import 'package:injectable/injectable.dart';
import '../services/app_lock_service.dart';
import '../services/theme_service.dart';
import '../services/archive_service.dart';
import '../services/biometric_service.dart';
import '../services/privacy_service.dart';
import '../../services/password_service.dart';
import '../../shared/utils/toast_utils.dart';

part 'app_store.g.dart';

/// 全局应用状态管理
@singleton
class AppStore = _AppStore with _$AppStore;

abstract class _AppStore with Store {
  final AppLockService _appLockService;
  final ThemeService _themeService;
  final ArchiveService _archiveService;
  final PasswordService _passwordService;
  final BiometricService _biometricService;
  final PrivacyService _privacyService;

  _AppStore(
    this._appLockService,
    this._themeService,
    this._archiveService,
    this._passwordService,
    this._biometricService,
    this._privacyService,
  );

  Timer? _lockTimer;

  // Observable状态
  @observable
  bool isLocked = false;

  @observable
  bool isInitialized = false;

  @observable
  DateTime? lastActivityTime;

  @observable
  ThemeMode themeMode = ThemeMode.system;

  @observable
  bool isSetupComplete = false;

  @observable
  bool isLoading = false;

  @observable
  String? errorMessage;

  @observable
  bool isPrivacyAgreed = false;

  /// 检查隐私协议状态
  @action
  Future<void> checkPrivacyAgreement() async {
    try {
      isPrivacyAgreed = await _privacyService.getAgreePrivacy();
    } catch (e) {
      print('检查隐私协议状态失败: $e');
      isPrivacyAgreed = false;
    }
  }

  /// 设置隐私协议同意状态
  @action
  Future<bool> setPrivacyAgreed(bool agreed) async {
    try {
      final success = await _privacyService.setAgreePrivacy(agreed);
      if (success) {
        isPrivacyAgreed = agreed;
      }
      return success;
    } catch (e) {
      print('设置隐私协议状态失败: $e');
      return false;
    }
  }

  /// 初始化应用（仅在同意隐私协议后调用）
  @action
  Future<void> initialize() async {
    isLoading = true;
    try {
      // 检查隐私协议状态
      await checkPrivacyAgreement();

      // 只有同意隐私协议才继续初始化
      if (!isPrivacyAgreed) {
        isLoading = false;
        return;
      }

      // 初始化密码服务
      isSetupComplete = await _passwordService.isSetupComplete();

      // 初始化主题
      themeMode = await _themeService.getThemeMode();

      isInitialized = true;

      // 如果已经设置了主密码，则启动时锁定应用
      if (isSetupComplete) {
        isLocked = true;
      }

      _startLockTimer();
    } catch (e) {
      errorMessage = '初始化失败: $e';
    } finally {
      isLoading = false;
    }
  }

  /// 更新活动时间
  @action
  void updateActivity() {
    lastActivityTime = DateTime.now();
    _resetLockTimer();
  }

  /// 解锁应用
  @action
  Future<bool> unlock(String password) async {
    try {
      final success = await _passwordService.verifyMasterPassword(password);
      if (success) {
        isLocked = false;
        updateActivity();
        errorMessage = null;
        return true;
      } else {
        errorMessage = '密码错误';
        return false;
      }
    } catch (e) {
      errorMessage = '验证密码时发生错误: $e';
      return false;
    }
  }

  /// 使用生物识别解锁应用
  @action
  Future<bool> unlockWithBiometric() async {
    try {
      // 检查生物识别是否可用和已启用
      final isEnabled = await _biometricService.isBiometricEnabled();
      if (!isEnabled) {
        errorMessage = '生物识别未启用';
        return false;
      }

      final result = await _biometricService.authenticate(
        localizedReason: '请验证您的身份以解锁应用',
      );

      if (result.isSuccess) {
        isLocked = false;
        updateActivity();
        errorMessage = null;
        return true;
      } else {
        errorMessage = result.message;
        return false;
      }
    } catch (e) {
      errorMessage = '生物识别解锁失败: $e';
      return false;
    }
  }

  /// 锁定应用
  @action
  void lock() {
    isLocked = true;
    _stopLockTimer();
  }

  /// 登出
  @action
  void logout() {
    lock();
    // 可以在这里添加清除敏感数据的逻辑
  }

  /// 设置主密码
  @action
  Future<bool> setMasterPassword(String password) async {
    isLoading = true;
    errorMessage = null;

    try {
      final success = await _passwordService.setMasterPassword(password);
      if (success) {
        isSetupComplete = true;
        return true;
      } else {
        errorMessage = '设置密码失败';
        return false;
      }
    } catch (e) {
      errorMessage = '设置密码时发生错误: $e';
      return false;
    } finally {
      isLoading = false;
    }
  }

  /// 切换主题模式
  @action
  Future<void> changeThemeMode(ThemeMode newThemeMode) async {
    themeMode = newThemeMode;
    await _themeService.setThemeMode(newThemeMode);
    ToastUtils.showSuccess(
        '主题已切换为${ThemeService.getThemeModeDisplayName(newThemeMode)}');
  }

  /// 重置密码并归档数据
  @action
  Future<bool> resetPasswordWithArchive() async {
    try {
      ToastUtils.showLoading('正在归档数据...');

      // 归档当前数据
      final archivePath = await _archiveService.archiveCurrentData();

      if (archivePath != null) {
        // 重置密码
        final success = await _passwordService.resetMasterPassword();

        if (success) {
          // 重置密码后更新状态
          isSetupComplete = false;
          isLocked = false; // 重置密码后解锁应用，允许跳转到设置页面
          errorMessage = null; // 清除错误信息
          ToastUtils.showSuccess('数据已归档，密码重置成功');
          return true;
        } else {
          ToastUtils.showError('密码重置失败');
          return false;
        }
      } else {
        ToastUtils.showError('数据归档失败');
        return false;
      }
    } catch (e) {
      ToastUtils.showError('重置密码时发生错误：$e');
      return false;
    }
  }

  /// 获取归档列表
  Future<List<ArchiveInfo>> getArchiveList() async {
    return await _archiveService.getArchiveList();
  }

  /// 删除归档
  Future<bool> deleteArchive(String fileName) async {
    final success = await _archiveService.deleteArchive(fileName);
    if (success) {
      ToastUtils.showSuccess('归档已删除');
    } else {
      ToastUtils.showError('删除归档失败');
    }
    return success;
  }

  /// 检查密码强度
  Map<String, dynamic> checkPasswordStrength(String password) {
    return _passwordService.checkPasswordStrength(password);
  }

  /// 生成建议密码
  String generateSuggestedPassword({int length = 16}) {
    return _passwordService.generateSuggestedPassword(length: length);
  }

  /// 清除错误信息
  @action
  void clearError() {
    errorMessage = null;
  }

  void _startLockTimer() {
    _resetLockTimer();
  }

  void _resetLockTimer() {
    _stopLockTimer();

    // 只有在应用已解锁且设置了主密码时才启动定时器
    if (!isLocked && isSetupComplete) {
      _lockTimer = Timer(const Duration(minutes: 5), () {
        lock();
      });
    }
  }

  void _stopLockTimer() {
    _lockTimer?.cancel();
    _lockTimer = null;
  }

  void dispose() {
    _stopLockTimer();
  }
}
