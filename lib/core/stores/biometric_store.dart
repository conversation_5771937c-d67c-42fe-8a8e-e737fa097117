import 'package:mobx/mobx.dart';
import 'package:injectable/injectable.dart';
import 'package:local_auth/local_auth.dart';
import '../services/biometric_service.dart';
import '../../shared/utils/toast_utils.dart';

part 'biometric_store.g.dart';

/// 生物识别状态管理
@singleton
class BiometricStore = _BiometricStore with _$BiometricStore;

abstract class _BiometricStore with Store {
  final BiometricService _biometricService;

  _BiometricStore(this._biometricService);

  // Observable状态
  @observable
  bool isDeviceSupported = false;

  @observable
  bool canCheckBiometrics = false;

  @observable
  bool isFingerprintAvailable = false;

  @observable
  bool isFaceAvailable = false;

  @observable
  bool isBiometricEnabled = false;

  @observable
  bool isAuthenticating = false;

  @observable
  bool isInitialized = false;

  @observable
  String? errorMessage;

  @observable
  ObservableList<BiometricType> availableBiometrics =
      ObservableList<BiometricType>();

  /// 初始化生物识别功能
  @action
  Future<void> initialize() async {
    if (isInitialized) return; // 避免重复初始化

    try {
      // 检查设备支持
      isDeviceSupported = await _biometricService.isDeviceSupported();

      if (!isDeviceSupported) {
        isInitialized = true;
        return;
      }

      // 检查生物识别可用性
      canCheckBiometrics = await _biometricService.canCheckBiometrics();

      if (canCheckBiometrics) {
        // 获取可用的生物识别类型
        final biometrics = await _biometricService.getAvailableBiometrics();
        availableBiometrics.clear();
        availableBiometrics.addAll(biometrics);

        // 检查具体类型
        isFingerprintAvailable =
            await _biometricService.isFingerprintAvailable();
        isFaceAvailable = await _biometricService.isFaceAvailable();
      }

      // 获取用户设置
      isBiometricEnabled = await _biometricService.isBiometricEnabled();

      isInitialized = true;
      errorMessage = null;
    } catch (e) {
      errorMessage = '初始化生物识别失败: $e';
      isInitialized = true; // 即使失败也标记为已初始化，避免重复初始化
    }
  }

  /// 执行生物识别认证
  @action
  Future<bool> authenticate({
    String? customReason,
  }) async {
    // 对于设置场景，允许在未启用时进行认证
    if (!isDeviceSupported || !canCheckBiometrics) {
      return false;
    }

    isAuthenticating = true;
    errorMessage = null;

    try {
      final result = await _biometricService.authenticate(
        localizedReason: customReason ?? '请验证您的身份以解锁应用',
        useErrorDialogs: true,
        stickyAuth: false,
      );

      if (result.isSuccess) {
        // 只在解锁场景显示成功提示，设置场景不显示
        if (customReason == null) {
          ToastUtils.showSuccess('生物识别验证成功');
        }
        return true;
      } else {
        errorMessage = result.message;
        // 根据错误类型决定是否显示提示
        if (result != BiometricAuthResult.failed) {
          // 其他错误显示toast，用户取消不显示
          ToastUtils.showError(result.message);
        }
        return false;
      }
    } catch (e) {
      errorMessage = '生物识别认证发生错误: $e';
      ToastUtils.showError(errorMessage!);
      return false;
    } finally {
      isAuthenticating = false;
    }
  }

  /// 设置生物识别启用状态
  @action
  Future<bool> setBiometricEnabled(bool enabled) async {
    if (!isDeviceSupported || !canCheckBiometrics) {
      ToastUtils.showError('设备不支持生物识别功能');
      return false;
    }

    if (enabled && availableBiometrics.isEmpty) {
      ToastUtils.showError('请先在系统设置中注册指纹或人脸');
      return false;
    }

    // 如果要启用生物识别，先进行一次认证确认
    if (enabled && !isBiometricEnabled) {
      final authResult = await authenticate(
        customReason: '请验证您的身份以启用生物识别解锁',
      );

      if (!authResult) {
        return false;
      }
    }

    try {
      final success = await _biometricService.setBiometricEnabled(enabled);

      if (success) {
        isBiometricEnabled = enabled;
        final successMsg = enabled ? '生物识别已启用' : '生物识别已禁用';
        ToastUtils.showSuccess(successMsg);
        return true;
      } else {
        ToastUtils.showError('保存生物识别设置失败');
        return false;
      }
    } catch (e) {
      errorMessage = '设置生物识别状态失败: $e';
      ToastUtils.showError(errorMessage!);
      return false;
    }
  }

  /// 停止认证
  @action
  Future<void> stopAuthentication() async {
    if (isAuthenticating) {
      await _biometricService.stopAuthentication();
      isAuthenticating = false;
    }
  }

  /// 清除错误信息
  @action
  void clearError() {
    errorMessage = null;
  }

  // Computed属性
  @computed
  bool get isAvailable =>
      isDeviceSupported && canCheckBiometrics && availableBiometrics.isNotEmpty;

  @computed
  bool get canEnableBiometric => isAvailable && !isBiometricEnabled;

  @computed
  bool get canUseBiometric => isAvailable && isBiometricEnabled;

  @computed
  String get biometricTypeText {
    if (isFingerprintAvailable && isFaceAvailable) {
      return '指纹或人脸识别';
    } else if (isFingerprintAvailable) {
      // Android上通常显示为生物识别，因为可能包含多种类型
      if (availableBiometrics.contains(BiometricType.weak) ||
          availableBiometrics.contains(BiometricType.strong)) {
        return '生物识别';
      }
      return '指纹识别';
    } else if (isFaceAvailable) {
      return '人脸识别';
    } else {
      return '生物识别';
    }
  }

  @computed
  String get statusText {
    if (!isDeviceSupported) {
      return '设备不支持生物识别';
    } else if (!canCheckBiometrics) {
      return '生物识别不可用';
    } else if (availableBiometrics.isEmpty) {
      return '请先在系统设置中注册指纹或人脸';
    } else if (isBiometricEnabled) {
      return '已启用${biometricTypeText}';
    } else {
      return '可使用${biometricTypeText}';
    }
  }
}
