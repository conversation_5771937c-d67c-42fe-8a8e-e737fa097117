import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:go_router/go_router.dart';
import 'package:password/pages/password_list/password_list_page.dart';
import 'package:password/pages/setup_password/setup_password_page.dart';
import 'package:password/pages/unlock/unlock_page.dart';
import '../di/injection.dart';
import '../stores/app_store.dart';
import 'router_path.dart';
import '../../features/password/account_edit/account_edit_page.dart';
import '../../features/password/account_detail/account_detail_page.dart';
import '../../features/category/category_manage/category_manage_page.dart';
import '../../features/category/category_edit/category_edit_page.dart';
import '../../features/tags/tag_manage/tag_manage_page.dart';
import '../../features/tags/tag_selection/tag_selection_page.dart';
import '../../features/settings/settings/settings_page.dart';
import '../../pages/splash/splash_screen.dart';
import '../../features/backup/webdav_config/webdav_config_page.dart';
import '../../features/backup/backup_manage/backup_manage_page.dart';
import '../../features/privacy/privacy_policy_page.dart';
import '../../features/privacy/service_agreement_page.dart';
import '../../features/search/search_page/search_page.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: RouterPath.splash,
    observers: [FlutterSmartDialog.observer],
    redirect: (context, state) {
      final appStore = getIt<AppStore>();
      final isInitialized = appStore.isInitialized;
      final isSetupComplete = appStore.isSetupComplete;
      final isLocked = appStore.isLocked;
      print(
          'isInitialized: $isInitialized, isSetupComplete: $isSetupComplete, isLocked: $isLocked');
      // 如果应用未初始化，跳转到启动页
      if (!isInitialized) {
        return RouterPath.splash;
      }

      // 如果未设置主密码，跳转到设置页
      if (!isSetupComplete) {
        return RouterPath.setup;
      }

      // 如果应用被锁定，跳转到解锁页
      if (isLocked) {
        return RouterPath.unlock;
      }

      // 如果当前在启动页、设置页或解锁页，但应用已解锁，跳转到主页
      if (state.uri.toString() == RouterPath.splash ||
          state.uri.toString() == RouterPath.setup ||
          state.uri.toString() == RouterPath.unlock) {
        return RouterPath.home;
      }

      return null;
    },
    routes: [
      GoRoute(
        path: RouterPath.splash,
        builder: (context, state) => const SplashPage(),
      ),
      GoRoute(
        path: RouterPath.setup,
        builder: (context, state) => const SetupPasswordPage(),
      ),
      GoRoute(
        path: RouterPath.unlock,
        builder: (context, state) => const UnlockPage(),
      ),
      GoRoute(
        path: RouterPath.home,
        builder: (context, state) => const PasswordListPage(),
      ),
      GoRoute(
        path: RouterPath.addAccount,
        name: 'add-account',
        builder: (context, state) => const AccountEditPage(),
      ),
      GoRoute(
        path: RouterPath.accountDetail,
        name: 'account-detail',
        builder: (context, state) {
          final accountId = state.pathParameters['id']!;
          return AccountDetailPage(accountId: accountId);
        },
      ),
      GoRoute(
        path: RouterPath.editAccount,
        name: 'edit-account',
        builder: (context, state) {
          final accountId = state.pathParameters['id']!;
          return AccountEditPage(accountId: accountId);
        },
      ),
      GoRoute(
        path: RouterPath.categories,
        name: 'categories',
        builder: (context, state) => const CategoryManagePage(),
      ),
      GoRoute(
        path: RouterPath.addCategory,
        name: 'add-category',
        builder: (context, state) => const CategoryEditPage(),
      ),
      GoRoute(
        path: RouterPath.editCategory,
        name: 'edit-category',
        builder: (context, state) {
          final categoryId = state.pathParameters['id']!;
          return CategoryEditPage(categoryId: categoryId);
        },
      ),
      GoRoute(
        path: RouterPath.settings,
        name: 'settings',
        builder: (context, state) => const SettingsPage(),
      ),
      GoRoute(
        path: RouterPath.webdavConfig,
        name: 'webdav-config',
        builder: (context, state) => const WebdavConfigPage(),
      ),
      GoRoute(
        path: RouterPath.backup,
        name: 'backup',
        builder: (context, state) => const BackupManagePage(),
      ),
      GoRoute(
        path: RouterPath.tags,
        name: 'tags',
        builder: (context, state) => const TagManagePage(),
      ),
      GoRoute(
        path: RouterPath.tagSelection,
        name: 'tag-selection',
        builder: (context, state) {
          final selectedTags = state.extra as List<String>? ?? [];
          return TagSelectionPage(initialSelectedTags: selectedTags);
        },
      ),
      GoRoute(
        path: RouterPath.privacyPolicy,
        name: 'privacy-policy',
        builder: (context, state) => const PrivacyPolicyPage(),
      ),
      GoRoute(
        path: RouterPath.serviceAgreement,
        name: 'service-agreement',
        builder: (context, state) => const ServiceAgreementPage(),
      ),
      GoRoute(
        path: RouterPath.search,
        name: 'search',
        builder: (context, state) => const SearchPage(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '页面未找到',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              '路径: ${state.uri}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(RouterPath.home),
              child: const Text('返回首页'),
            ),
          ],
        ),
      ),
    ),
  );
}
