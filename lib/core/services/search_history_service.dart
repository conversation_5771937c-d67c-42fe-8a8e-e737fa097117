import 'package:shared_preferences/shared_preferences.dart';
import 'package:injectable/injectable.dart';

/// 搜索历史管理服务
/// 负责搜索历史的存储、获取和管理
@singleton
class SearchHistoryService {
  static const String _searchHistoryKey = 'search_history';
  static const int _maxHistoryCount = 20;

  /// 添加搜索历史
  /// 如果已存在相同的搜索词，会将其移到最前面
  Future<bool> addSearchHistory(String query) async {
    try {
      if (query.trim().isEmpty) return false;
      
      final prefs = await SharedPreferences.getInstance();
      final historyList = await getSearchHistory();
      
      // 移除已存在的相同搜索词
      historyList.removeWhere((item) => item.toLowerCase() == query.toLowerCase());
      
      // 添加到最前面
      historyList.insert(0, query.trim());
      
      // 限制历史记录数量
      if (historyList.length > _maxHistoryCount) {
        historyList.removeRange(_maxHistoryCount, historyList.length);
      }
      
      // 保存到本地存储
      await prefs.setStringList(_searchHistoryKey, historyList);
      return true;
    } catch (e) {
      print('添加搜索历史失败: $e');
      return false;
    }
  }

  /// 获取搜索历史列表
  /// 返回按时间倒序排列的搜索历史
  Future<List<String>> getSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getStringList(_searchHistoryKey) ?? [];
    } catch (e) {
      print('获取搜索历史失败: $e');
      return [];
    }
  }

  /// 删除指定的搜索历史项
  Future<bool> removeSearchHistory(String query) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyList = await getSearchHistory();
      
      // 移除指定项
      historyList.removeWhere((item) => item.toLowerCase() == query.toLowerCase());
      
      // 保存更新后的列表
      await prefs.setStringList(_searchHistoryKey, historyList);
      return true;
    } catch (e) {
      print('删除搜索历史失败: $e');
      return false;
    }
  }

  /// 清空所有搜索历史
  Future<bool> clearSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_searchHistoryKey);
      return true;
    } catch (e) {
      print('清空搜索历史失败: $e');
      return false;
    }
  }

  /// 获取搜索历史数量
  Future<int> getSearchHistoryCount() async {
    try {
      final historyList = await getSearchHistory();
      return historyList.length;
    } catch (e) {
      print('获取搜索历史数量失败: $e');
      return 0;
    }
  }

  /// 检查是否存在指定的搜索历史
  Future<bool> hasSearchHistory(String query) async {
    try {
      final historyList = await getSearchHistory();
      return historyList.any((item) => item.toLowerCase() == query.toLowerCase());
    } catch (e) {
      print('检查搜索历史失败: $e');
      return false;
    }
  }

  /// 获取最近的搜索历史（限制数量）
  Future<List<String>> getRecentSearchHistory({int limit = 10}) async {
    try {
      final historyList = await getSearchHistory();
      return historyList.take(limit).toList();
    } catch (e) {
      print('获取最近搜索历史失败: $e');
      return [];
    }
  }

  /// 搜索历史中的模糊匹配
  Future<List<String>> searchInHistory(String query) async {
    try {
      if (query.trim().isEmpty) return [];
      
      final historyList = await getSearchHistory();
      final lowerQuery = query.toLowerCase();
      
      return historyList
          .where((item) => item.toLowerCase().contains(lowerQuery))
          .toList();
    } catch (e) {
      print('搜索历史匹配失败: $e');
      return [];
    }
  }
}
