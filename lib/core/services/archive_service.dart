import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:injectable/injectable.dart';

/// 数据归档服务
/// 负责在重置密码时归档当前数据
@singleton
class ArchiveService {
  static const String _archivePrefix = 'archive_';
  static const String _archiveIndexKey = 'archive_index';

  /// 归档当前数据
  /// 返回归档文件路径
  Future<String?> archiveCurrentData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final appDir = await getApplicationDocumentsDirectory();

      // 创建归档目录
      final archiveDir = Directory('${appDir.path}/archives');
      if (!await archiveDir.exists()) {
        await archiveDir.create(recursive: true);
      }

      // 生成归档文件名
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final archiveFileName = 'archive_$timestamp.json';
      final archiveFile = File('${archiveDir.path}/$archiveFileName');

      // 收集所有需要归档的数据
      final archiveData = <String, dynamic>{
        'timestamp': timestamp,
        'date': DateTime.now().toIso8601String(),
        'version': '1.0.0',
        'data': <String, dynamic>{},
      };

      // 获取所有SharedPreferences数据
      final allKeys = prefs.getKeys();
      for (final key in allKeys) {
        final value = prefs.get(key);
        if (value != null) {
          archiveData['data'][key] = value;
        }
      }

      // 写入归档文件
      await archiveFile.writeAsString(
        const JsonEncoder.withIndent('  ').convert(archiveData),
      );

      // 更新归档索引
      await _updateArchiveIndex(archiveFileName, timestamp);

      return archiveFile.path;
    } catch (e) {
      print('归档数据失败: $e');
      return null;
    }
  }

  /// 获取所有归档文件列表
  Future<List<ArchiveInfo>> getArchiveList() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final archiveDir = Directory('${appDir.path}/archives');

      if (!await archiveDir.exists()) {
        return [];
      }

      final files = await archiveDir.list().toList();
      final archives = <ArchiveInfo>[];

      for (final file in files) {
        if (file is File && file.path.endsWith('.json')) {
          try {
            final content = await file.readAsString();
            final data = jsonDecode(content) as Map<String, dynamic>;

            archives.add(ArchiveInfo(
              fileName: file.path.split('/').last,
              filePath: file.path,
              timestamp: data['timestamp'] as int,
              date: DateTime.parse(data['date'] as String),
              version: data['version'] as String? ?? '1.0.0',
            ));
          } catch (e) {
            print('读取归档文件失败: ${file.path}, $e');
          }
        }
      }

      // 按时间倒序排列
      archives.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      return archives;
    } catch (e) {
      print('获取归档列表失败: $e');
      return [];
    }
  }

  /// 删除归档文件
  Future<bool> deleteArchive(String fileName) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final archiveFile = File('${appDir.path}/archives/$fileName');

      if (await archiveFile.exists()) {
        await archiveFile.delete();
        return true;
      }
      return false;
    } catch (e) {
      print('删除归档文件失败: $e');
      return false;
    }
  }

  /// 获取归档文件详情
  Future<Map<String, dynamic>?> getArchiveDetails(String fileName) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final archiveFile = File('${appDir.path}/archives/$fileName');

      if (await archiveFile.exists()) {
        final content = await archiveFile.readAsString();
        return jsonDecode(content) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      print('读取归档详情失败: $e');
      return null;
    }
  }

  /// 更新归档索引
  Future<void> _updateArchiveIndex(String fileName, int timestamp) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final indexList = prefs.getStringList(_archiveIndexKey) ?? <String>[];

      final indexEntry = jsonEncode({
        'fileName': fileName,
        'timestamp': timestamp,
      });

      indexList.add(indexEntry);
      await prefs.setStringList(_archiveIndexKey, indexList);
    } catch (e) {
      print('更新归档索引失败: $e');
    }
  }

  /// 清理旧归档（保留最近10个）
  Future<void> cleanOldArchives({int keepCount = 10}) async {
    try {
      final archives = await getArchiveList();

      if (archives.length > keepCount) {
        final toDelete = archives.skip(keepCount);

        for (final archive in toDelete) {
          await deleteArchive(archive.fileName);
        }
      }
    } catch (e) {
      print('清理旧归档失败: $e');
    }
  }
}

/// 归档信息类
class ArchiveInfo {
  final String fileName;
  final String filePath;
  final int timestamp;
  final DateTime date;
  final String version;

  const ArchiveInfo({
    required this.fileName,
    required this.filePath,
    required this.timestamp,
    required this.date,
    required this.version,
  });

  String get formattedDate {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} '
        '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  String get sizeInfo {
    // 这里可以添加文件大小信息
    return '数据归档';
  }
}
