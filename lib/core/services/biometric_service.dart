import 'package:injectable/injectable.dart';
import 'package:local_auth/local_auth.dart';
import 'package:local_auth/error_codes.dart' as auth_error;
import 'package:shared_preferences/shared_preferences.dart';

/// 生物识别服务
/// 负责管理指纹和人脸识别功能
@singleton
class BiometricService {
  static const String _biometricEnabledKey = 'biometric_enabled';

  final LocalAuthentication _localAuth = LocalAuthentication();

  /// 检查设备是否支持生物识别
  Future<bool> isDeviceSupported() async {
    try {
      return await _localAuth.isDeviceSupported();
    } catch (e) {
      print('检查设备支持失败: $e');
      return false;
    }
  }

  /// 检查是否有可用的生物识别方法
  Future<bool> canCheckBiometrics() async {
    try {
      return await _localAuth.canCheckBiometrics;
    } catch (e) {
      print('检查生物识别可用性失败: $e');
      return false;
    }
  }

  /// 获取可用的生物识别类型
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      print('获取可用生物识别类型失败: $e');
      return [];
    }
  }

  /// 检查是否支持指纹识别
  Future<bool> isFingerprintAvailable() async {
    try {
      final availableBiometrics = await getAvailableBiometrics();
      // Android上通常返回weak/strong，iOS上返回fingerprint
      // 我们将weak/strong都视为指纹识别可用
      return availableBiometrics.contains(BiometricType.fingerprint) ||
          availableBiometrics.contains(BiometricType.weak) ||
          availableBiometrics.contains(BiometricType.strong);
    } catch (e) {
      print('检查指纹识别可用性失败: $e');
      return false;
    }
  }

  /// 检查是否支持人脸识别
  Future<bool> isFaceAvailable() async {
    try {
      final availableBiometrics = await getAvailableBiometrics();
      // iOS上返回face，Android上通常不单独区分人脸识别
      // 在Android上，我们不显示单独的人脸识别按钮
      return availableBiometrics.contains(BiometricType.face);
    } catch (e) {
      print('检查人脸识别可用性失败: $e');
      return false;
    }
  }

  /// 执行生物识别认证
  Future<BiometricAuthResult> authenticate({
    String localizedReason = '请验证您的身份以解锁应用',
    bool useErrorDialogs = true,
    bool stickyAuth = false,
  }) async {
    print('BiometricService.authenticate called with reason: $localizedReason');

    try {
      // 检查设备支持
      final isSupported = await isDeviceSupported();
      print('Device supported: $isSupported');
      if (!isSupported) {
        return BiometricAuthResult.deviceNotSupported;
      }

      // 检查生物识别可用性
      final canCheck = await canCheckBiometrics();
      print('Can check biometrics: $canCheck');
      if (!canCheck) {
        return BiometricAuthResult.notAvailable;
      }

      // 获取可用的生物识别类型
      final availableBiometrics = await getAvailableBiometrics();
      print('Available biometrics: $availableBiometrics');
      if (availableBiometrics.isEmpty) {
        return BiometricAuthResult.notEnrolled;
      }

      // 执行认证
      print('Starting authentication...');
      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: localizedReason,
        options: AuthenticationOptions(
          useErrorDialogs: useErrorDialogs,
          stickyAuth: stickyAuth,
          biometricOnly: true,
        ),
      );

      print('Authentication result: $didAuthenticate');
      return didAuthenticate
          ? BiometricAuthResult.success
          : BiometricAuthResult.failed;
    } catch (e) {
      print('生物识别认证异常: $e');
      print('Exception type: ${e.runtimeType}');

      final errorString = e.toString().toLowerCase();

      // 处理特定错误
      if (errorString.contains('not_available') ||
          errorString.contains('notavailable')) {
        print('Error type: not available');
        return BiometricAuthResult.notAvailable;
      } else if (errorString.contains('not_enrolled') ||
          errorString.contains('notenrolled')) {
        print('Error type: not enrolled');
        return BiometricAuthResult.notEnrolled;
      } else if (errorString.contains('locked_out') ||
          errorString.contains('lockedout')) {
        print('Error type: locked out');
        return BiometricAuthResult.lockedOut;
      } else if (errorString.contains('permanently_locked_out') ||
          errorString.contains('permanentlylockedout')) {
        print('Error type: permanently locked out');
        return BiometricAuthResult.permanentlyLockedOut;
      } else if (errorString.contains('user_cancel') ||
          errorString.contains('usercancel')) {
        print('Error type: user cancelled');
        return BiometricAuthResult.failed;
      }

      print('Error type: generic error');
      return BiometricAuthResult.error;
    }
  }

  /// 获取用户是否启用了生物识别
  Future<bool> isBiometricEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_biometricEnabledKey) ?? false;
    } catch (e) {
      print('获取生物识别设置失败: $e');
      return false;
    }
  }

  /// 设置生物识别启用状态
  Future<bool> setBiometricEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setBool(_biometricEnabledKey, enabled);
    } catch (e) {
      print('设置生物识别状态失败: $e');
      return false;
    }
  }

  /// 停止认证（如果正在进行）
  Future<void> stopAuthentication() async {
    try {
      await _localAuth.stopAuthentication();
    } catch (e) {
      print('停止生物识别认证失败: $e');
    }
  }
}

/// 生物识别认证结果
enum BiometricAuthResult {
  /// 认证成功
  success,

  /// 认证失败
  failed,

  /// 设备不支持生物识别
  deviceNotSupported,

  /// 生物识别不可用
  notAvailable,

  /// 未注册生物识别
  notEnrolled,

  /// 暂时锁定
  lockedOut,

  /// 永久锁定
  permanentlyLockedOut,

  /// 其他错误
  error,
}

/// 生物识别认证结果扩展方法
extension BiometricAuthResultExtension on BiometricAuthResult {
  /// 获取错误消息
  String get message {
    switch (this) {
      case BiometricAuthResult.success:
        return '认证成功';
      case BiometricAuthResult.failed:
        return '认证失败，请重试';
      case BiometricAuthResult.deviceNotSupported:
        return '设备不支持生物识别';
      case BiometricAuthResult.notAvailable:
        return '生物识别不可用';
      case BiometricAuthResult.notEnrolled:
        return '请先在系统设置中注册指纹或人脸';
      case BiometricAuthResult.lockedOut:
        return '生物识别已被暂时锁定，请稍后重试';
      case BiometricAuthResult.permanentlyLockedOut:
        return '生物识别已被永久锁定，请使用密码解锁';
      case BiometricAuthResult.error:
        return '生物识别发生错误';
    }
  }

  /// 是否为成功结果
  bool get isSuccess => this == BiometricAuthResult.success;

  /// 是否为错误结果
  bool get isError => !isSuccess;
}
