import 'dart:async';
import 'package:injectable/injectable.dart';

/// 应用锁定服务
/// 负责管理应用的自动锁定功能
@singleton
class AppLockService {
  static const Duration _lockTimeout = Duration(minutes: 5);

  Timer? _lockTimer;
  DateTime? _lastActivityTime;

  /// 开始锁定计时器
  void startLockTimer(VoidCallback onLock) {
    _resetLockTimer(onLock);
  }

  /// 重置锁定计时器
  void resetLockTimer(VoidCallback onLock) {
    _resetLockTimer(onLock);
  }

  /// 停止锁定计时器
  void stopLockTimer() {
    _lockTimer?.cancel();
    _lockTimer = null;
  }

  /// 更新最后活动时间
  void updateLastActivity() {
    _lastActivityTime = DateTime.now();
  }

  /// 获取最后活动时间
  DateTime? get lastActivityTime => _lastActivityTime;

  /// 检查是否应该锁定
  bool shouldLock() {
    if (_lastActivityTime == null) return false;

    final now = DateTime.now();
    final timeSinceLastActivity = now.difference(_lastActivityTime!);

    return timeSinceLastActivity >= _lockTimeout;
  }

  /// 获取剩余时间
  Duration? getRemainingTime() {
    if (_lastActivityTime == null) return null;

    final now = DateTime.now();
    final timeSinceLastActivity = now.difference(_lastActivityTime!);
    final remaining = _lockTimeout - timeSinceLastActivity;

    return remaining.isNegative ? Duration.zero : remaining;
  }

  void _resetLockTimer(VoidCallback onLock) {
    stopLockTimer();
    updateLastActivity();

    _lockTimer = Timer(_lockTimeout, () {
      onLock();
    });
  }

  /// 销毁服务
  void dispose() {
    stopLockTimer();
  }
}

typedef VoidCallback = void Function();
