import 'package:flutter/material.dart';
import 'package:mobx/mobx.dart';
import 'package:injectable/injectable.dart';
import 'package:password/data/google/google_parse_repo.dart';
import 'package:password/features/category/category_manage/category_manage_store.dart';
import '../../../../core/stores/app_store.dart';
import '../../../services/account_store.dart';
import '../../../model/account_model.dart';
import '../../../../shared/models/category_model.dart';
import '../../../../services/csv_import_service.dart';
import '../../../../shared/utils/toast_utils.dart';

part 'password_list_store.g.dart';

/// 密码列表页面状态管理
@injectable
class PasswordListStore = _PasswordListStore with _$PasswordListStore;

abstract class _PasswordListStore with Store {
  final AppStore appStore;
  final CategoryManageStore categoryStore;
  final AccountStore accountStore;
  final CsvImportService csvImportService;
  final GoogleParseRepo googleParseRepo;

  _PasswordListStore(
      {required this.appStore,
      required this.categoryStore,
      required this.accountStore,
      required this.csvImportService,
      required this.googleParseRepo}) {}

  @observable
  bool isLoading = false;

  @observable
  String? errorMessage;

  @observable
  String? selectedCategoryId;

  @observable
  bool isImporting = false;

  // Computed values
  @computed
  List<AccountModel> get filteredAccounts {
    List<AccountModel> accounts = accountStore.accounts.toList();

    // 按分类过滤
    if (selectedCategoryId != null && selectedCategoryId!.isNotEmpty) {
      accounts = accounts
          .where((account) => account.categoryId == selectedCategoryId)
          .toList();
    }

    return accounts;
  }

  @computed
  List<CategoryModel> get availableCategories {
    return categoryStore.categories.toList();
  }

  @computed
  bool get hasAccounts => accountStore.accounts.isNotEmpty;

  @computed
  bool get isInitialized => !categoryStore.isLoading && !accountStore.isLoading;

  // Actions
  @action
  Future<void> initialize() async {
    isLoading = true;
    errorMessage = null;

    try {
      await categoryStore.initialize();
      appStore.updateActivity();
    } catch (e) {
      errorMessage = '初始化失败：$e';
    } finally {
      isLoading = false;
    }
  }

  @action
  void clearError() {
    errorMessage = null;
  }

  @action
  void setSelectedCategory(String? categoryId) {
    selectedCategoryId = categoryId;
  }

  /// 获取分类信息
  CategoryModel? getCategoryById(String? categoryId) {
    if (categoryId == null) return null;
    return categoryStore.getCategoryById(categoryId);
  }

  /// 锁定应用
  void lockApp() {
    appStore.lock();
  }

  /// 获取应用主题模式
  ThemeMode get themeMode => appStore.themeMode;

  /// 设置主题模式
  @action
  Future<void> setThemeMode(ThemeMode mode) async {
    await appStore.changeThemeMode(mode);
  }

  /// 从CSV文件导入账号数据
  @action
  Future<void> importFromCsv() async {
    // 调用CSV导入服务
    final result = await csvImportService.importFromCsv();
    final accounts = result.data ?? List.empty();

    if (accounts.isEmpty) {
      ToastUtils.showError('没有找到有效的账号数据');
      return;
    }
    accountStore.addAccounts(accounts);
  }

  /// 从Google Authenticator二维码导入账号数据
  @action
  Future<void> importFromGoogle(String qrCode) async {
    final accounts = await googleParseRepo.parseGoogleAuthenticatorData(qrCode);
    accountStore.addAccounts(accounts);
  }
}
