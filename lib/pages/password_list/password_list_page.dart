import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:password/core/router/router_path.dart';
import 'package:password/features/backup/qr_scanner/qr_scanner_utils.dart';
import 'package:password/pages/password_list/dialogs/more_action_popup.dart';
import 'package:password/pages/password_list/widgets/category_tab.dart';

import '../../../../core/di/injection.dart';
import 'password_list_store.dart';
import 'widgets/custom_header.dart';
import 'widgets/simple_account_list_item.dart';

class PasswordListPage extends StatefulWidget {
  const PasswordListPage({super.key});

  @override
  State<PasswordListPage> createState() => _PasswordListPageState();
}

class _PasswordListPageState extends State<PasswordListPage> {
  late final PasswordListStore store;

  @override
  void initState() {
    super.initState();
    store = getIt<PasswordListStore>();
    _initializeStore();
  }

  Future<void> _initializeStore() async {
    await store.initialize();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _onAccountTap(account) {
    context.push('/account-detail/${account.id}');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomHeader(
        centerWidget: Observer(builder: (context) {
          return Align(
            alignment: Alignment.center,
            child: CategoryTab(
              maxItemWidth: 120,
              categories: store.availableCategories,
              value: store.selectedCategoryId,
              onSelected: store.setSelectedCategory,
            ),
          );
        }),
        onSearch: () => context.push(RouterPath.search),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () async {
          final qrCode = await QrImportService.importFromCamera(context);
          if (qrCode == null) return;
          store.importFromGoogle(qrCode);
        },
        icon: const Icon(Icons.file_upload),
        label: Text('Google导入'),
      ),
      body: Column(
        children: [
          // 主要内容区域
          Expanded(
            child: Observer(
              builder: (context) {
                if (store.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (store.errorMessage != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          store.errorMessage!,
                          style: const TextStyle(color: Colors.red),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            store.clearError();
                            _initializeStore();
                          },
                          child: const Text('重试'),
                        ),
                      ],
                    ),
                  );
                }

                if (!store.hasAccounts) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.lock_outline,
                          size: 64,
                          color: Colors.grey,
                        ),
                        SizedBox(height: 16),
                        Text(
                          '暂无账号',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          '点击右上角菜单添加第一个账号',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: store.filteredAccounts.length,
                  itemBuilder: (context, index) {
                    final account = store.filteredAccounts[index];
                    return AccountListItem(
                      account: account,
                      category: store.getCategoryById(account.categoryId),
                      onTap: () => _onAccountTap(account),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
