import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:password/pages/password_list/dialogs/more_action_popup.dart';
import '../../../../../core/di/injection.dart';
import '../../../../core/router/router_path.dart';
import '../../../../../features/user/stores/user_store.dart';
import '../../../../../shared/utils/toast_utils.dart';

class CustomHeader extends StatelessWidget implements PreferredSizeWidget {
  final VoidCallback onSearch;
  final Widget centerWidget;

  const CustomHeader({
    super.key,
    required this.centerWidget,
    required this.onSearch,
  });

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    final userStore = getIt<UserStore>();

    return Container(
      color: Colors.white,
      child: Safe<PERSON>rea(
        child: Container(
          height: kToolbarHeight,
          alignment: Alignment.center,
          child: Row(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Observer(
                  builder: (context) {
                    return GestureDetector(
                      onTap: () => context.push(RouterPath.settings),
                      child: Stack(
                        children: [
                          CircleAvatar(
                            radius: 18,
                            backgroundColor: const Color(0xFF4286EA),
                            backgroundImage: userStore.userAvatar != null
                                ? NetworkImage(userStore.userAvatar!)
                                : null,
                            child: userStore.userAvatar == null
                                ? Text(
                                    userStore.userAvatarText,
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  )
                                : null,
                          ),
                          Positioned(
                            right: 0,
                            bottom: 0,
                            child: Container(
                              width: 14,
                              height: 14,
                              decoration: const BoxDecoration(
                                color: Colors.amber,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.star,
                                color: Colors.white,
                                size: 10,
                              ),
                            ),
                          )
                        ],
                      ),
                    );
                  },
                ),
              ),
              Expanded(child: centerWidget),
              // 右侧：操作按钮
              IconButton(
                icon: const Icon(
                  Icons.search,
                  color: Colors.black54,
                ),
                onPressed: onSearch,
              ),
              Builder(builder: (c) {
                return IconButton(
                  icon: const Icon(
                    Icons.add_circle_outline,
                    color: Colors.black54,
                  ),
                  onPressed: () async {
                    final path = await showMoreActionPopup(c);
                    if (path != null && context.mounted) {
                      context.push(path);
                    }
                  },
                );
              })
            ],
          ),
        ),
      ),
    );
  }
}
