import 'package:flutter/material.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

import '../../../../shared/models/category_model.dart';

class CategoryTab extends StatefulWidget {
  final String? value;
  final List<CategoryModel> categories;
  final ValueChanged<String?> onSelected;
  final double maxItemWidth;
  final double itemIndent;

  const CategoryTab({
    super.key,
    this.value,
    this.maxItemWidth = 200,
    this.itemIndent = 4,
    required this.onSelected,
    required this.categories,
  });

  @override
  State<CategoryTab> createState() => _CategoryTabState();
}

class _CategoryTabState extends State<CategoryTab> {
  late int selectedIndex;
  late ItemScrollController _scrollController;
  late ItemPositionsListener _positionsListener;

  List<CategoryModel?> get categories => [null, ...widget.categories];

  final Map<int, GlobalKey> _itemKeys = {};

  @override
  void initState() {
    super.initState();
    _scrollController = ItemScrollController();
    _positionsListener = ItemPositionsListener.create();
    _updateItemKeys();

    // 初始化选中项
    final index = categories.indexWhere((c) => c?.id == widget.value);
    selectedIndex = index != -1 ? index : 0;
  }

  @override
  void didUpdateWidget(covariant CategoryTab oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.categories != widget.categories) {
      _updateItemKeys();

      // Update selected index if needed
      final index = categories.indexWhere((c) => c?.id == widget.value);
      selectedIndex = index != -1 ? index : 0;
    }
  }

  void _updateItemKeys() {
    _itemKeys.clear();
    for (int i = 0; i < categories.length; i++) {
      _itemKeys[i] = GlobalKey();
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _scrollToCenter(int index) async {
    await Future.delayed(Duration.zero);
    print('Scroll2Center');
    // 获取滚动容器的宽度
    final RenderBox? containerRenderBox =
        context.findRenderObject() as RenderBox?;
    if (containerRenderBox == null || !containerRenderBox.hasSize) return;
    final containerWidth = containerRenderBox.size.width;

    // 获取目标项的key
    final itemKey = _itemKeys[index];
    print('itemKey:$itemKey');
    if (itemKey?.currentContext == null) return;

    // 获取目标项的尺寸
    final RenderBox itemRenderBox =
        itemKey?.currentContext!.findRenderObject() as RenderBox;
    final itemWidth = itemRenderBox.size.width;

    // 计算alignment值
    double alignment = ((containerWidth - itemWidth) / 2) / containerWidth;

    print(
        'Container width: $containerWidth, Item width: $itemWidth, Alignment: $alignment');

    _scrollController.scrollTo(
      index: index,
      alignment: alignment.clamp(-1.0, 1.0),
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return ScrollablePositionedList.builder(
      itemScrollController: _scrollController,
      itemPositionsListener: _positionsListener,
      scrollDirection: Axis.horizontal,
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final selected = index == selectedIndex;
        return Container(
          key: _itemKeys[index],
          // 分配唯一的key
          // color: selected ? theme.colorScheme.primary : Colors.transparent,
          height: 40,
          alignment: Alignment.center,
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              setState(() {
                selectedIndex = index;
              });
              // 在下一帧执行滚动，确保组件已重建完成
              WidgetsBinding.instance.addPostFrameCallback((_) {
                widget.onSelected(categories[index]?.id);
                _scrollToCenter(index);
              });
            },
            child: ConstrainedBox(
              constraints: BoxConstraints(maxWidth: widget.maxItemWidth),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: widget.itemIndent),
                child: Text(
                  categories[index]?.name ?? "全部",
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  style: selected
                      ? theme.textTheme.titleLarge!.copyWith(
                          fontSize: 24,
                          height: 1,
                          color: theme.colorScheme.primary)
                      : theme.textTheme.bodyMedium!.copyWith(height: 1),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
