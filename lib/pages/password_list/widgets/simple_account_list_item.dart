import 'package:flutter/material.dart';
import 'package:password/features/tags/tag_manage/tag_manage_store.dart';
import '../../../../model/account_model.dart';
import '../../../../../shared/models/category_model.dart';
import '../../../../../core/di/injection.dart';

/// 简化的账号列表项组件
class AccountListItem extends StatelessWidget {
  final AccountModel account;
  final CategoryModel? category;
  final VoidCallback? onTap;

  const AccountListItem({
    super.key,
    required this.account,
    this.category,
    this.onTap,
  });

  TagManageStore get _tagStore => getIt<TagManageStore>();

  Color _getCategoryColor() {
    if (category?.color != null) {
      try {
        return Color(int.parse(category!.color.replaceFirst('#', '0xFF')));
      } catch (e) {
        return const Color(0xFF4286EA);
      }
    }
    return const Color(0xFF4286EA);
  }

  IconData _getCategoryIcon() {
    if (category?.icon != null) {
      switch (category!.icon) {
        case 'group':
          return Icons.group;
        case 'email':
          return Icons.email;
        case 'account_balance':
          return Icons.account_balance;
        case 'shopping_cart':
          return Icons.shopping_cart;
        case 'movie':
          return Icons.movie;
        case 'work':
          return Icons.work;
        case 'school':
          return Icons.school;
        case 'flight':
          return Icons.flight;
        case 'local_hospital':
          return Icons.local_hospital;
        case 'more_horiz':
          return Icons.more_horiz;
        default:
          return Icons.account_circle;
      }
    }
    return Icons.account_circle;
  }

  Color _hexToColor(String hex) {
    hex = hex.replaceAll('#', '');
    if (hex.length == 6) {
      hex = 'FF$hex';
    }
    return Color(int.parse(hex, radix: 16));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // 左侧图标
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: _getCategoryColor().withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getCategoryIcon(),
                    color: _getCategoryColor(),
                    size: 24,
                  ),
                ),

                const SizedBox(width: 16),

                // 中间内容
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 账号标题
                      Text(
                        account.title,
                        style: const TextStyle(
                          color: Colors.black87,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 4),

                      // 用户名
                      Text(
                        account.username,
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 14,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 6),

                      // // 分类和标签
                      // if (category != null || account.tags.isNotEmpty)
                      //   Row(
                      //     children: [
                      //       // 分类
                      //       if (category != null) ...[
                      //         Container(
                      //           padding: const EdgeInsets.symmetric(
                      //               horizontal: 8, vertical: 4),
                      //           decoration: BoxDecoration(
                      //             color: _getCategoryColor()
                      //                 .withValues(alpha: 0.1),
                      //             borderRadius: BorderRadius.circular(12),
                      //             border: Border.all(
                      //               color: _getCategoryColor()
                      //                   .withValues(alpha: 0.3),
                      //               width: 1,
                      //             ),
                      //           ),
                      //           child: Text(
                      //             category!.name,
                      //             style: TextStyle(
                      //               color: _getCategoryColor(),
                      //               fontSize: 12,
                      //               fontWeight: FontWeight.w500,
                      //             ),
                      //           ),
                      //         ),
                      //         const SizedBox(width: 8),
                      //       ],
                      //
                      //       // 标签
                      //       if (account.tags.isNotEmpty) ...[
                      //         Expanded(
                      //           child: Wrap(
                      //             spacing: 6,
                      //             runSpacing: 4,
                      //             children: account.tags.take(2).map((tagName) {
                      //               final tagModel =
                      //                   _tagStore.getTagByName(tagName);
                      //               final color = tagModel?.color ?? '#4ECDC4';
                      //               return Container(
                      //                 padding: const EdgeInsets.symmetric(
                      //                     horizontal: 8, vertical: 4),
                      //                 decoration: BoxDecoration(
                      //                   color: _hexToColor(color)
                      //                       .withValues(alpha: 0.1),
                      //                   borderRadius: BorderRadius.circular(12),
                      //                   border: Border.all(
                      //                     color: _hexToColor(color)
                      //                         .withValues(alpha: 0.3),
                      //                     width: 1,
                      //                   ),
                      //                 ),
                      //                 child: Text(
                      //                   tagName,
                      //                   style: TextStyle(
                      //                     color: _hexToColor(color),
                      //                     fontSize: 12,
                      //                     fontWeight: FontWeight.w500,
                      //                   ),
                      //                 ),
                      //               );
                      //             }).toList(),
                      //           ),
                      //         ),
                      //       ],
                      //     ],
                      //   ),
                    ],
                  ),
                ),

                // 右侧状态指示器
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 2FA状态指示器
                    Visibility(
                      visible: account.twoFactorEnabled,
                      child: Container(
                        margin: const EdgeInsets.only(bottom: 4),
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.green.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                            color: Colors.green.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: const Icon(
                          Icons.security,
                          color: Colors.green,
                          size: 12,
                        ),
                      ),
                    ),
                    // 收藏状态指示器
                    if (account.isFavorite)
                      Container(
                        margin: const EdgeInsets.only(bottom: 4),
                        child: const Icon(
                          Icons.favorite,
                          color: Colors.red,
                          size: 16,
                        ),
                      ),
                    // 箭头图标
                    Icon(
                      Icons.chevron_right,
                      color: Colors.grey.shade400,
                      size: 20,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
