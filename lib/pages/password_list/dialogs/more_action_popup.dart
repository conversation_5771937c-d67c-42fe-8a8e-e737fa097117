import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:password/core/router/router_path.dart';
import 'package:password/pages/password_list/dialogs/more_action_item.dart';

class MoreActionPopup extends StatelessWidget {
  final List<MoreActionItem> items;

  const MoreActionPopup({super.key, required this.items});

  @override
  Widget build(BuildContext context) {
    ColorScheme colorScheme = Theme.of(context).colorScheme;

    return Material(
      color: Colors.transparent,
      child: Stack(
        children: [
          // 三角指示器 (放在弹窗上方)
          Positioned(
            right: 16,
            top: 2,
            child: CustomPaint(
              size: const Size(16, 8),
              painter: _<PERSON><PERSON>ain<PERSON>(
                  backgroundColor: colorScheme.surfaceContainer),
            ),
          ),
          Container(
            margin: const EdgeInsets.only(top: 8, right: 12),
            width: 180,
            decoration: BoxDecoration(
              color: colorScheme.surfaceContainer,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                for (var item in items) ...[
                  _buildMenuItem(item),
                  _buildDivider(),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建菜单项
  Widget _buildMenuItem(MoreActionItem item) {
    return SizedBox(
      height: 48,
      child: InkWell(
          onTap: () {
            SmartDialog.dismiss(result: item.id);
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Icon(item.icon, size: 20, color: Colors.black87),
                const SizedBox(width: 12),
                Text(item.title, style: const TextStyle(fontSize: 15)),
              ],
            ),
          )),
    );
  }

  // 分割线
  Widget _buildDivider() {
    return Divider(
      height: 1,
      thickness: 0.5,
      indent: 16,
      endIndent: 16,
      color: Colors.grey[200],
    );
  }
}

// 三角形绘制
class _TrianglePainter extends CustomPainter {
  final Color backgroundColor;

  _TrianglePainter({required this.backgroundColor});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill
      ..strokeWidth = 1;

    final path = Path()
      ..moveTo(size.width / 2, 0)
      ..lineTo(0, size.height)
      ..lineTo(size.width, size.height)
      ..close();

    canvas.drawPath(path, paint);

    // 添加边框线 (可选)
    canvas.drawPath(
      path,
      paint
        ..color = backgroundColor
        ..style = PaintingStyle.fill,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

Future<String?> showMoreActionPopup(BuildContext context) async {
  final items = [
    const MoreActionItem(
        id: RouterPath.addAccount, title: '添加账号', icon: Icons.person_add_alt_1),
    const MoreActionItem(
        id: 'favorite', title: '收藏', icon: Icons.favorite_border),
    const MoreActionItem(id: 'delete', title: '删除', icon: Icons.delete),
  ];

  final result = await SmartDialog.showAttach(
    targetContext: context,
    alignment: Alignment.bottomCenter,
    adjustBuilder: (AttachParam attachParam) {
      return AttachAdjustParam(
        alignment: Alignment.bottomCenter,
        builder: (_) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 30),
            child: attachParam.selfWidget,
          );
        },
      );
    },
    builder: (_) {
      return MoreActionPopup(
        items: items,
      );
    },
  );
  return result;
}
