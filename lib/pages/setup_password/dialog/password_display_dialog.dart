import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:password/shared/utils/encryption_utils.dart';

class PasswordDisplayDialog extends StatefulWidget {
  final String password;
  final VoidCallback? onConfirm;

  const PasswordDisplayDialog({
    super.key,
    required this.password,
    this.onConfirm,
  });

  @override
  State<PasswordDisplayDialog> createState() => _PasswordDisplayDialogState();
}

class _PasswordDisplayDialogState extends State<PasswordDisplayDialog> {
  bool _isPasswordVisible = true;
  bool _hasConfirmedSaved = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.colorScheme;
    final screenHeight = MediaQuery.of(context).size.height;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxHeight: screenHeight * 0.6, // 固定为屏幕高度的60%
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题栏（固定不滚动）
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 16, 24, 8),
              child: Row(
                children: [
                  Icon(
                    Icons.security_rounded,
                    color: colors.primary,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    '重要安全提醒',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close_rounded),
                    onPressed: () => Navigator.pop(context, false),
                    padding: EdgeInsets.zero,
                    visualDensity: VisualDensity.compact,
                  ),
                ],
              ),
            ),

            // 可滚动内容区域
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.fromLTRB(24, 0, 24, 16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 8),

                    // 警告卡片
                    _buildWarningCard(theme, colors),

                    const SizedBox(height: 20),

                    // 密码显示区域
                    Text(
                      '您的主密码',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildPasswordDisplay(theme),

                    const SizedBox(height: 20),

                    // 保存建议
                    _buildSaveSuggestions(theme, colors),

                    const SizedBox(height: 16),

                    // 确认复选框
                    _buildConfirmationCheckbox(theme),

                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),

            // 底部操作按钮（固定不滚动）
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 0, 24, 16),
              child: _buildActionButtons(theme, colors),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWarningCard(ThemeData theme, ColorScheme colors) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colors.errorContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.warning_rounded,
                color: colors.onErrorContainer,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '安全警告',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: colors.onErrorContainer,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '这是您的主密码，用于加密保护所有其他密码。'
            '忘记此密码将无法恢复您的数据！',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colors.onErrorContainer,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPasswordDisplay(ThemeData theme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.dividerColor,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: SelectableText(
                  _isPasswordVisible
                      ? EncryptionUtils.formatPasswordForDisplay(
                          widget.password)
                      : '•' * widget.password.length,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontFamily: 'RobotoMono',
                    letterSpacing: 1.5,
                    height: 1.4,
                  ),
                ),
              ),
              IconButton(
                onPressed: () {
                  setState(() {
                    _isPasswordVisible = !_isPasswordVisible;
                  });
                },
                icon: Icon(
                  _isPasswordVisible
                      ? Icons.visibility_off_rounded
                      : Icons.visibility_rounded,
                  color: theme.colorScheme.primary,
                ),
                tooltip: _isPasswordVisible ? '隐藏密码' : '显示密码',
                style: IconButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Align(
            alignment: Alignment.centerRight,
            child: FilledButton.tonalIcon(
              onPressed: () {
                Clipboard.setData(ClipboardData(text: widget.password));
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text('密码已复制到剪贴板'),
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                );
              },
              icon: const Icon(Icons.copy_rounded, size: 18),
              label: const Text('复制密码'),
              style: FilledButton.styleFrom(
                visualDensity: VisualDensity.compact,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSaveSuggestions(ThemeData theme, ColorScheme colors) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colors.primaryContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_rounded,
                color: colors.onPrimaryContainer,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '安全保存建议',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: colors.onPrimaryContainer,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSuggestionItem('📸 截图保存到加密相册'),
              _buildSuggestionItem('📝 手写记录在安全笔记本'),
              _buildSuggestionItem('🔐 使用其他密码管理器备份'),
              _buildSuggestionItem('👪 告知信任的家庭成员'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestionItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(width: 4),
          Text('•', style: TextStyle(color: Colors.grey.shade600)),
          const SizedBox(width: 8),
          Expanded(child: Text(text)),
        ],
      ),
    );
  }

  Widget _buildConfirmationCheckbox(ThemeData theme) {
    return Row(
      children: [
        Checkbox(
          value: _hasConfirmedSaved,
          onChanged: (value) {
            setState(() {
              _hasConfirmedSaved = value ?? false;
            });
          },
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            '我已安全保存此密码，并理解忘记密码将导致数据不可恢复',
            style: theme.textTheme.bodyMedium,
            maxLines: 2,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(ThemeData theme, ColorScheme colors) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop(false);
          },
          child: const Text('稍后保存'),
        ),
        const SizedBox(width: 12),
        FilledButton(
          onPressed: _hasConfirmedSaved
              ? () {
                  Navigator.of(context).pop(true);
                  widget.onConfirm?.call();
                }
              : null,
          style: FilledButton.styleFrom(
            backgroundColor: colors.primary,
            foregroundColor: colors.onPrimary,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
          child: const Text('确认保存'),
        ),
      ],
    );
  }
}

Future<bool?> showPasswordDisplayDialog(
  BuildContext context,
  String password, {
  VoidCallback? onConfirm,
}) {
  return showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (context) => PasswordDisplayDialog(
      password: password,
      onConfirm: onConfirm,
    ),
  );
}
