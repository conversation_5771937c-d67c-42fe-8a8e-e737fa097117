import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:mobx/mobx.dart';
import 'package:password/core/di/injection.dart';
import 'package:password/core/router/router_path.dart';
import 'package:password/pages/setup_password/setup_password_vm.dart';
import 'package:password/shared/utils/toast_utils.dart';
import 'package:password/widgets/password_textfield.dart';

import 'dialog/password_display_dialog.dart';

class SetupPasswordPage extends StatefulWidget {
  const SetupPasswordPage({super.key});

  @override
  State<SetupPasswordPage> createState() => _SetupPasswordPageState();
}

class _SetupPasswordPageState extends State<SetupPasswordPage> {
  late final SetupPasswordVM vm;
  final pwdFocusNode = FocusNode();
  final confirmPwdFocusNode = FocusNode();

  // 表单控制器
  final formKey = GlobalKey<FormState>();

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  @override
  void initState() {
    super.initState();
    vm = getIt();
    initEvent();
  }

  initEvent() {
    reaction((_) => vm.event, (event) {
      if (event == null) return;
      vm.clearEvent();
      // 设置密码成功，跳转到首页
      if (event == SetupPasswordEvent.success) {
        context.replace(RouterPath.home);
      }
    });
  }

  @override
  void dispose() {
    pwdFocusNode.dispose();
    confirmPwdFocusNode.dispose();
    super.dispose();
  }

  Future<void> _setupPassword() async {
    if (!(formKey.currentState?.validate() ?? false)) {
      return;
    }
    if (vm.password != vm.confirmPassword) {
      ToastUtils.showError('两次输入的密码不一致');
      return;
    }
    if (vm.password.length < 6) {
      ToastUtils.showError('密码长度至少6位');
      return;
    }
    if (vm.confirmPassword.length < 6) {
      ToastUtils.showError('确认密码长度至少6位');
      return;
    }
    final result = await showPasswordDisplayDialog(context, vm.password);
    if (result == true) {
      vm.saveMasterPassword();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF4286EA),
      resizeToAvoidBottomInset: true,
      body: LayoutBuilder(
        builder: (context, constraints) {
          return SingleChildScrollView(
            physics: const ClampingScrollPhysics(), // 禁用弹性效果
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: constraints.maxHeight,
              ),
              child: Padding(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom,
                ),
                child: IntrinsicHeight(
                  child: Column(
                    children: [
                      // 顶部留白，使表单保持在居中偏上位置
                      const Spacer(flex: 1),
                      // 表单内容
                      _buildContent(),
                      // 底部留白，键盘弹出时这部分会被压缩
                      const Spacer(flex: 2),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          const SizedBox(height: 32),
          _buildForm(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.security,
            size: 60,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 24),
        const Text(
          '设置主密码',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildForm() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      constraints: const BoxConstraints(maxWidth: 500),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Form(
        key: formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildPasswordField(),
            _buildPasswordStrengthIndicator(),
            const SizedBox(height: 16),
            _buildConfirmPasswordField(),
            const SizedBox(height: 24),
            _buildSetupButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildPasswordField() {
    return Observer(
      builder: (context) {
        return PasswordTextField(
          fillColor: colorScheme.surfaceContainer,
          focusNode: pwdFocusNode,
          hintText: '请输入主密码',
          value: vm.password,
          onChange: vm.setPassword,
          textInputAction: TextInputAction.next,
          onSubmitted: (v) {
            FocusScope.of(context).requestFocus(confirmPwdFocusNode);
          },
        );
      },
    );
  }

  Widget _buildPasswordStrengthIndicator() {
    return Observer(
      builder: (context) {
        return Visibility(
          visible: vm.showStrengthIndicator,
          child: Padding(
            padding: const EdgeInsets.only(top: 12),
            child: Row(
              children: [
                const Text('密码强度：'),
                Text(
                  vm.passwordStrengthText,
                  style: TextStyle(
                    color: vm.getStrengthColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: LinearProgressIndicator(
                    value: (vm.passwordStrength + 1) / 3,
                    backgroundColor: Colors.grey.shade300,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      vm.getStrengthColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildConfirmPasswordField() {
    return Observer(
      builder: (context) {
        return PasswordTextField(
          fillColor: colorScheme.surfaceContainer,
          focusNode: confirmPwdFocusNode,
          hintText: '确认密码',
          value: vm.confirmPassword,
          onChange: vm.setConfirmPassword,
          textInputAction: TextInputAction.next,
          onSubmitted: (v) {},
        );
      },
    );
  }

  Widget _buildSetupButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _setupPassword,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
          backgroundColor: const Color(0xFF4286EA),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: const Text(
          '设置主密码',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}
