import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:mobx/mobx.dart';
import 'package:password/core/stores/app_store.dart';
import 'package:password/services/password_service.dart';
import 'package:password/shared/utils/encryption_utils.dart';

part 'setup_password_vm.g.dart';

enum SetupPasswordEvent { success }

@injectable
class SetupPasswordVM = _SetupPasswordVM with _$SetupPasswordVM;

abstract class _SetupPasswordVM with Store {
  final PasswordService _passwordService;
  final AppStore _appStore;

  _SetupPasswordVM(this._passwordService, this._appStore) {
    init();
  }

  @observable
  SetupPasswordEvent? event;

  @observable
  String password = "";

  @observable
  String confirmPassword = "";

  @computed
  int get passwordStrength => EncryptionUtils.checkPasswordStrength(password);

  @computed
  Color get getStrengthColor {
    switch (passwordStrength) {
      case 0:
        return Colors.red;
      case 1:
        return Colors.orange;
      case 2:
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  @computed
  String get passwordStrengthText =>
      EncryptionUtils.getPasswordStrengthText(passwordStrength);

  @computed
  bool get showStrengthIndicator => password.isNotEmpty;

  init() async {}

  @action
  void setPassword(String value) {
    password = value;
  }

  @action
  void setConfirmPassword(String value) {
    confirmPassword = value;
  }

  @action
  Future<void> saveMasterPassword() async {
    final success = await _passwordService.setMasterPassword(password);
    if (success) {
      await _appStore.initialize();
      event = SetupPasswordEvent.success;
    }
  }

  @action
  clearEvent() {
    event = null;
  }
}
