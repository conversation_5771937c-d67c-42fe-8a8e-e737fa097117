import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:go_router/go_router.dart';
import 'package:password/core/router/router_path.dart';

class AgreePrivacyDialog extends StatelessWidget {
  const AgreePrivacyDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return ConstrainedBox(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
      ),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 36),
        decoration: BoxDecoration(
          color: colorScheme.surfaceContainer,
          borderRadius: BorderRadius.circular(16.0),
        ),
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '提示',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 16),
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '欢迎使用记密码！',
                      style: textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      '为了给您提供更好的服务，我们需要您同意我们的隐私政策和服务协议。',
                      style: textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      '我们承诺保护您的隐私，所有密码信息仅在本地存储，不会上传到服务器。',
                      style: textTheme.labelSmall,
                    ),
                    const SizedBox(height: 12),
                    Wrap(
                      children: [
                        Text(
                          '请您阅读',
                          style: textTheme.bodySmall,
                        ),
                        GestureDetector(
                          onTap: () =>
                              context.push(RouterPath.serviceAgreement),
                          child: Text(
                            '《服务协议》',
                            style: textTheme.titleSmall!
                                .copyWith(color: colorScheme.primary),
                          ),
                        ),
                        Text(
                          '和',
                          style: textTheme.bodySmall,
                        ),
                        GestureDetector(
                          onTap: () => context.push(RouterPath.privacyPolicy),
                          child: Text(
                            '《隐私政策》',
                            style: textTheme.titleSmall!
                                .copyWith(color: colorScheme.primary),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            Column(
              children: [
                FilledButton(
                  style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20.0),
                    ),
                  ),
                  onPressed: () {
                    SmartDialog.dismiss(result: true);
                  },
                  child: Center(
                    child: Text(
                      '同意',
                      style: textTheme.titleMedium!
                          .copyWith(color: colorScheme.onPrimary),
                    ),
                  ),
                ),
                const SizedBox(height: 4),
                TextButton(
                  child: Center(
                    child: Text(
                      '不同意',
                      style: textTheme.labelSmall,
                    ),
                  ),
                  onPressed: () {
                    SmartDialog.dismiss(result: false);
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
