import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:go_router/go_router.dart';
import 'package:mobx/mobx.dart';
import 'package:password/pages/splash/dialog/agree_privacy_dialog.dart';
import 'package:password/pages/splash/splash_vm.dart';

import '../../core/di/injection.dart';

/// 启动屏幕
/// 用于初始化应用状态并根据状态跳转到相应页面
class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  late final SplashVm vm;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  TextTheme get textTheme => Theme.of(context).textTheme;

  @override
  void initState() {
    super.initState();
    vm = getIt<SplashVm>();
    initEvent();
    vm.init();
  }

  initEvent() {
    reaction((_) => vm.event, (event) {
      if (event == null) return;
      vm.clearEvent();
      if (event == SplashEvent.showPrivacyDialog) {
        // UI准备好后显示隐私协议对话框
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _showPrivacyDialog();
        });
        return;
      }
      if (event == SplashEvent.handleRedirect) {
        _handleRedirect();
        return;
      }
    });
  }

  _showPrivacyDialog() async {
    final result = await SmartDialog.show(
      alignment: Alignment.center,
      clickMaskDismiss: false,
      builder: (c) => const AgreePrivacyDialog(),
    );
    if (result == true) {
      vm.agreePrivacy();
    } else {
      SystemNavigator.pop();
    }
  }

  _handleRedirect() {
    print('跳转到: ${vm.routePath}');
    try {
      context.replace(vm.routePath);
    } catch (e) {
      print('跳转失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF4286EA),
              Color(0xFF5A9BF0),
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 应用图标
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.security,
                  size: 80,
                  color: Colors.white,
                ),
              ),

              const SizedBox(height: 32),

              // 应用名称
              const Text(
                '记密码',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),

              const SizedBox(height: 8),

              // 应用描述
              Text(
                '安全的密码管理工具',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white.withValues(alpha: 0.9),
                ),
              ),

              const SizedBox(height: 48),

              // 加载指示器
              Observer(
                builder: (context) => Column(
                  children: [
                    const SizedBox(
                      width: 32,
                      height: 32,
                      child: CircularProgressIndicator(
                        strokeWidth: 3,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      vm.isInitialized ? '启动中...' : '初始化中...',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 64),

              // 版本信息
              Text(
                'v1.0.0',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white.withValues(alpha: 0.6),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
