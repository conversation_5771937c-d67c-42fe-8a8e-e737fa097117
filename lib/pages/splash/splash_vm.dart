import 'package:injectable/injectable.dart';
import 'package:mobx/mobx.dart';
import 'package:password/core/router/router_path.dart';
import 'package:password/core/stores/app_store.dart';

part 'splash_vm.g.dart';

enum SplashEvent {
  showPrivacyDialog,
  handleRedirect,
}

@injectable
class SplashVm = _SplashVm with _$SplashVm;

abstract class _SplashVm with Store {
  final AppStore _appStore;

  @observable
  SplashEvent? event;

  @computed
  bool get isInitialized => _appStore.isInitialized;

  String routePath = RouterPath.home;

  _SplashVm(
    this._appStore,
  );

  init() async {
    // 1. 检查是否需要显示隐私协议
    final needShowPrivacy = _appStore.isPrivacyAgreed;
    if (!needShowPrivacy) {
      event = SplashEvent.showPrivacyDialog;
    } else {
      initializeAfterPrivacyAgreed();
    }
  }

  @action
  Future<void> initializeAfterPrivacyAgreed() async {
    try {
      await _appStore.initialize();
      // 未设置主密码，跳转到设置页
      if (!_appStore.isSetupComplete) {
        routePath = RouterPath.setup;
      } else if (_appStore.isLocked) {
        routePath = RouterPath.unlock;
      }
      event = SplashEvent.handleRedirect;
    } catch (e) {
      print('初始化失败: $e');
    }
  }

  @action
  agreePrivacy() async {
    await _appStore.setPrivacyAgreed(true);
    initializeAfterPrivacyAgreed();
  }

  @action
  clearEvent() {
    event = null;
  }
}
