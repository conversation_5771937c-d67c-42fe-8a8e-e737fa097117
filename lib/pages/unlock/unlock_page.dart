import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:mobx/mobx.dart';
import 'package:password/pages/unlock/unlock_vm.dart';
import 'package:password/widgets/password_textfield.dart';
import '../../../core/di/injection.dart';
import '../../../core/stores/app_store.dart';
import '../../../core/stores/biometric_store.dart';
import '../../../core/router/router_path.dart';
import '../../../shared/utils/toast_utils.dart';

/// 解锁页面
/// 当应用被锁定时显示，要求用户输入主密码解锁
class UnlockPage extends StatefulWidget {
  const UnlockPage({super.key});

  @override
  State<UnlockPage> createState() => _UnlockPageState();
}

class _UnlockPageState extends State<UnlockPage> {
  late UnlockVM vm;

  late final BiometricStore biometricStore;

  @override
  void initState() {
    super.initState();
    vm = getIt<UnlockVM>();
    biometricStore = getIt<BiometricStore>();
    _initializeBiometric();
    initEvent();
  }

  initEvent() {
    reaction((_) => vm.event, (event) {
      if (event == null) return;
      vm.clearEvent();
      print('解锁事件: $event');
      // 解锁成功，跳转到首页
      if (event == UnlockEvent.unlocked) {
        context.replace(RouterPath.home);
      }
    });
  }

  Future<void> _initializeBiometric() async {
    await biometricStore.initialize();
    // 如果生物识别可用且已启用，自动弹出验证
    if (biometricStore.canUseBiometric) {
      vm.unlockWithBiometric();
    }
  }

  Future<void> _resetPassword() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.red),
            SizedBox(width: 8),
            Text('重置主密码'),
          ],
        ),
        content: const Text(
          '重置主密码将：\n\n'
          '1. 归档当前所有数据到本地文件\n'
          '2. 清除当前主密码设置\n'
          '3. 需要重新设置新的主密码\n\n'
          '此操作不可撤销，确定要继续吗？',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('确认重置'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await vm.resetPasswordWithArchive();
      if (success && mounted) {
        // 重置成功后，让路由系统自动重定向到设置页面
        // 由于 isSetupComplete = false 且 isLocked = false，路由会自动跳转到 /setup
        context.go(RouterPath.splash);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: isDark
                ? [
                    const Color(0xFF1F2937), // gray-800
                    const Color(0xFF111827), // gray-900
                  ]
                : [
                    const Color(0xFFF8FAFC), // slate-50
                    const Color(0xFFE2E8F0), // slate-200
                  ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 可滚动的主要内容区域
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    children: [
                      // 顶部间距，使内容居中偏上
                      SizedBox(
                        height: MediaQuery.of(context).size.height * 0.1,
                      ),

                      // 应用图标
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          color: isDark
                              ? Colors.white.withOpacity(0.1)
                              : Colors.white.withOpacity(0.8),
                          border: Border.all(
                            color: isDark
                                ? Colors.white.withOpacity(0.1)
                                : Colors.black.withOpacity(0.1),
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: isDark
                                  ? Colors.black.withOpacity(0.2)
                                  : Colors.black.withOpacity(0.1),
                              blurRadius: 20,
                              offset: const Offset(0, 8),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.security,
                          size: 40,
                          color:
                              isDark ? Colors.white : const Color(0xFF1F2937),
                        ),
                      ),

                      const SizedBox(height: 24),

                      // 应用标题
                      Text(
                        '记密码',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color:
                              isDark ? Colors.white : const Color(0xFF1F2937),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '您的安全密码管家',
                        style: TextStyle(
                          fontSize: 16,
                          color: isDark
                              ? Colors.grey.shade400
                              : Colors.grey.shade600,
                        ),
                      ),

                      const SizedBox(height: 48),

                      // 密码输入表单
                      Column(
                        children: [
                          // 密码输入框和生物识别按钮
                          Row(
                            children: [
                              // 密码输入框

                              Expanded(
                                child: Observer(builder: (context) {
                                  return PasswordTextField(
                                    autoFocus: true,
                                    hintText: "请输入主密码",
                                    onChange: vm.setPassword,
                                    value: vm.password,
                                  );
                                }),
                              ),

                              // 生物识别按钮
                              Observer(
                                builder: (context) {
                                  if (!biometricStore.canUseBiometric) {
                                    return const SizedBox.shrink();
                                  }

                                  return Container(
                                    margin: const EdgeInsets.only(left: 12),
                                    child: _buildBiometricButton(
                                      icon: Icons.fingerprint,
                                      onTap: vm.unlockWithBiometric,
                                      isLoading:
                                          biometricStore.isAuthenticating,
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),

                          const SizedBox(height: 24),

                          // 解锁按钮
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: () => vm.unlock(),
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 16,
                                ),
                                backgroundColor: isDark
                                    ? const Color(0xFF2563EB) // blue-600
                                    : const Color(0xFF1D4ED8),
                                // blue-700
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                elevation: isDark ? 0 : 2,
                                shadowColor: isDark
                                    ? null
                                    : Colors.black.withOpacity(0.1),
                              ),
                              child: const Text(
                                '解锁密码库',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 32),

                      // 底部帮助链接
                      const SizedBox(height: 32),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          TextButton(
                            onPressed: _resetPassword,
                            child: Text(
                              '忘记密码?',
                              style: TextStyle(
                                color: isDark
                                    ? const Color(0xFF60A5FA) // blue-400
                                    : const Color(0xFF2563EB), // blue-600
                                fontSize: 14,
                              ),
                            ),
                          ),
                          Text(
                            ' | ',
                            style: TextStyle(
                              color: isDark
                                  ? Colors.grey.shade600
                                  : Colors.grey.shade500,
                              fontSize: 14,
                            ),
                          ),
                          TextButton(
                            onPressed: () {
                              // TODO: 实现创建新账户功能
                            },
                            child: Text(
                              '创建新账户',
                              style: TextStyle(
                                color: isDark
                                    ? const Color(0xFF60A5FA) // blue-400
                                    : const Color(0xFF2563EB), // blue-600
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              // 固定在底部的提示信息
              Container(
                padding: const EdgeInsets.all(24.0),
                child: Text(
                  '为了保护您的数据安全，应用在5分钟无操作后会自动锁定',
                  style: TextStyle(
                    fontSize: 12,
                    color: isDark ? Colors.grey.shade600 : Colors.grey.shade500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBiometricButton({
    required IconData icon,
    required VoidCallback onTap,
    required bool isLoading,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: isLoading ? null : onTap,
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: isDark
              ? Colors.white.withOpacity(0.05)
              : Colors.white.withOpacity(0.8),
          borderRadius: BorderRadius.circular(24),
          border: Border.all(
            color: isDark
                ? Colors.white.withOpacity(0.1)
                : Colors.black.withOpacity(0.1),
            width: 1,
          ),
          boxShadow: isDark
              ? null
              : [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
        ),
        child: isLoading
            ? Center(
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      isDark ? Colors.white : const Color(0xFF1F2937),
                    ),
                  ),
                ),
              )
            : Icon(
                icon,
                color: isDark ? Colors.white : const Color(0xFF1F2937),
                size: 24,
              ),
      ),
    );
  }
}
