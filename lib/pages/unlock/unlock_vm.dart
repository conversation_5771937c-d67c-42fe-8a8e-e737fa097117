import 'package:injectable/injectable.dart';

import 'package:mobx/mobx.dart';
import 'package:password/core/services/biometric_service.dart';
import 'package:password/core/stores/app_store.dart';
import 'package:password/services/password_service.dart';
import 'package:password/shared/utils/toast_utils.dart';

part 'unlock_vm.g.dart';

enum UnlockEvent { unlocked }

@injectable
class UnlockVM = _UnlockVM with _$UnlockVM;

abstract class _UnlockVM with Store {
  final PasswordService _passwordService;
  final AppStore _appStore;
  final BiometricService _biometricService;

  @observable
  String password = "";

  @observable
  UnlockEvent? event;

  _UnlockVM(this._passwordService, this._appStore, this._biometricService) {
    init();
  }

  init() async {}

  @action
  void setPassword(String value) {
    password = value;
  }

  /// 解锁应用
  @action
  Future<void> unlock() async {
    final success = await _passwordService.verifyMasterPassword(password);
    if (success) {
      unlockSuccess();
      return;
    }
    ToastUtils.showError("密码错误");
  }

  /// 使用生物识别解锁应用
  @action
  Future<void> unlockWithBiometric() async {
    // 检查生物识别是否可用和已启用
    final isEnabled = await _biometricService.isBiometricEnabled();
    if (!isEnabled) {
      ToastUtils.showError('生物识别未启用');
      return;
    }

    final result = await _biometricService.authenticate(
        localizedReason: '请验证您的身份以解锁应用', useErrorDialogs: false);

    if (result.isSuccess) {
      unlockSuccess();
      return;
    }
    ToastUtils.showError(result.message);
  }

  @action
  unlockSuccess() {
    _appStore.updateActivity();
    _appStore.initialize();
    event = UnlockEvent.unlocked;
  }

  /// 重置密码并归档数据
  @action
  resetPasswordWithArchive() async {
    // try {
    //   ToastUtils.showLoading('正在归档数据...');
    //
    //   // 归档当前数据
    //   final archivePath = await _archiveService.archiveCurrentData();
    //
    //   if (archivePath != null) {
    //     // 重置密码
    //     final success = await _passwordService.resetMasterPassword();
    //
    //     if (success) {
    //       // 重置密码后更新状态
    //       isSetupComplete = false;
    //       isLocked = false; // 重置密码后解锁应用，允许跳转到设置页面
    //       errorMessage = null; // 清除错误信息
    //       ToastUtils.showSuccess('数据已归档，密码重置成功');
    //       return true;
    //     } else {
    //       ToastUtils.showError('密码重置失败');
    //       return false;
    //     }
    //   } else {
    //     ToastUtils.showError('数据归档失败');
    //     return false;
    //   }
    // } catch (e) {
    //   ToastUtils.showError('重置密码时发生错误：$e');
    //   return false;
    // }
  }

  @action
  clearEvent() {
    event = null;
  }
}
