import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'core/di/injection.dart';
import 'core/router/app_router.dart';
import 'core/stores/app_store.dart';
import 'core/services/theme_service.dart';

void main() {
  setup().then((_) {
    runApp(const PasswordApp());
  });
}

class PasswordApp extends StatefulWidget {
  const PasswordApp({super.key});

  @override
  State<PasswordApp> createState() => _PasswordAppState();
}

class _PasswordAppState extends State<PasswordApp> {
  late final AppStore appStore;

  @override
  void initState() {
    super.initState();
    appStore = getIt<AppStore>();
  }

  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (context) => MaterialApp.router(
        title: '记密码',
        debugShowCheckedModeBanner: false,
        theme: ThemeService.lightTheme,
        darkTheme: ThemeService.darkTheme,
        themeMode: appStore.themeMode,
        routerConfig: AppRouter.router,
        builder: FlutterSmartDialog.init(),
      ),
    );
  }
}
