import 'package:json_annotation/json_annotation.dart';

part 'app_config.g.dart';

@JsonSerializable()
class AppConfig {
  final bool agreePrivacy;

  AppConfig({required this.agreePrivacy});

  factory AppConfig.fromJson(Map<String, dynamic> json) =>
      _$AppConfigFromJson(json);

  Map<String, dynamic> toJson() => _$AppConfigToJson(this);

  factory AppConfig.defaultConfig() {
    return AppConfig(agreePrivacy: false);
  }

  AppConfig copyWith({bool? agreePrivacy}) {
    return AppConfig(agreePrivacy: agreePrivacy ?? this.agreePrivacy);
  }
}
