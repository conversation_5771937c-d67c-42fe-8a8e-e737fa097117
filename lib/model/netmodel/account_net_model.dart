import 'package:json_annotation/json_annotation.dart';
import 'package:password/model/account_model.dart';
import 'package:password/model/netmodel/otp_net_model.dart';
import 'package:uuid/uuid.dart';

part 'account_net_model.g.dart';

/// 网络账号数据模型
/// 专门用于网络数据传输和CSV导入
/// 支持更灵活的字段配置，允许更多字段为空
@JsonSerializable()
class AccountNetModel {
  final String id;
  final String title;
  final String username;
  final String? password;
  final String? email;
  final String? website;
  final String? notes;
  final String? categoryId; // 允许为空，导入时可能没有分类
  final List<String> tags;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? lastUsed;
  final bool isFavorite;

  // 2fa相关数据
  final OtpParametersNetModel? otpParameters;

  const AccountNetModel(
      {required this.id,
      required this.title,
      required this.username,
      this.password,
      this.email,
      this.website,
      this.notes,
      this.categoryId, // 可空，适合导入场景
      this.tags = const [],
      this.createdAt,
      this.updatedAt,
      this.lastUsed,
      this.isFavorite = false,
      this.otpParameters});

  /// 从CSV数据创建实例
  /// 专门用于CSV导入，自动生成ID和时间戳
  factory AccountNetModel.fromCsvData({
    required String name,
    required String username,
    String? password,
    String? website,
    String? notes,
  }) {
    final now = DateTime.now();
    final id = Uuid().v4();

    return AccountNetModel(
      id: id,
      title: name,
      username: username,
      password: password,
      website: website?.isNotEmpty == true ? website : null,
      notes: notes?.isNotEmpty == true ? notes : null,
      tags: const [],
      createdAt: now,
      updatedAt: now,
      lastUsed: null,
      isFavorite: false,
    );
  }

  /// 转换为本地AccountModel
  /// 如果categoryId为null，使用提供的默认分类ID
  AccountModel toAccountModel() {
    return AccountModel(
        id: id,
        title: title,
        username: username,
        password: password ?? "",
        email: email,
        website: website,
        notes: notes,
        categoryId: categoryId,
        tags: tags,
        createdAt: createdAt,
        updatedAt: updatedAt,
        lastUsed: lastUsed,
        isFavorite: isFavorite,
        otpParameters: otpParameters?.asBizModel());
  }

  /// 从AccountModel创建NetModel
  factory AccountNetModel.fromAccountModel(AccountModel account) {
    return AccountNetModel(
      id: account.id,
      title: account.title,
      username: account.username,
      password: account.password,
      email: account.email,
      website: account.website,
      notes: account.notes,
      categoryId: account.categoryId,
      tags: account.tags,
      createdAt: account.createdAt,
      updatedAt: account.updatedAt,
      lastUsed: account.lastUsed,
      isFavorite: account.isFavorite,
      otpParameters:
          OtpParametersNetModel.fromOtpParametersModel(account.otpParameters),
    );
  }

  /// 复制并修改
  AccountNetModel copyWith({
    String? id,
    String? title,
    String? username,
    String? password,
    String? email,
    String? website,
    String? notes,
    String? categoryId,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastUsed,
    bool? isFavorite,
    bool clearCategoryId = false,
    bool clearTwoFactorSecret = false,
    OtpParametersNetModel? otpParameters,
  }) {
    return AccountNetModel(
        id: id ?? this.id,
        title: title ?? this.title,
        username: username ?? this.username,
        password: password ?? this.password,
        email: email ?? this.email,
        website: website ?? this.website,
        notes: notes ?? this.notes,
        categoryId: clearCategoryId ? null : (categoryId ?? this.categoryId),
        tags: tags ?? this.tags,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        lastUsed: lastUsed ?? this.lastUsed,
        isFavorite: isFavorite ?? this.isFavorite,
        otpParameters: otpParameters ?? this.otpParameters);
  }

  factory AccountNetModel.fromJson(Map<String, dynamic> json) =>
      _$AccountNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$AccountNetModelToJson(this);
}
