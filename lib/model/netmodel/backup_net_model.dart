import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';
import 'package:password/model/netmodel/account_net_model.dart';
import 'package:password/model/netmodel/category_net_model.dart';

part 'backup_net_model.g.dart';

/// 备份数据模型
@JsonSerializable()
class BackupNetModel {
  final String? version;
  final DateTime? createdAt;
  final List<AccountNetModel?>? accounts;
  final List<CategoryNetModel?>? categories;

  const BackupNetModel({
    required this.version,
    required this.createdAt,
    required this.accounts,
    required this.categories,
  });

  factory BackupNetModel.fromJson(Map<String, dynamic> json) =>
      _$BackupNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$BackupNetModelToJson(this);

  /// 转换为JSON字符串
  String toJsonString() {
    return const JsonEncoder.withIndent('  ').convert(toJson());
  }

  /// 默认数据
  factory BackupNetModel.defaultData() {
    return BackupNetModel(
      version: '1.0.0',
      createdAt: DateTime.now(),
      accounts: [],
      categories: [],
    );
  }

  /// 从JSON字符串创建实例
  factory BackupNetModel.fromJsonString(String jsonString) {
    final json = jsonDecode(jsonString) as Map<String, dynamic>;
    return BackupNetModel.fromJson(json);
  }

  /// 创建副本
  BackupNetModel copyWith({
    String? version,
    DateTime? createdAt,
    List<AccountNetModel>? accounts,
    List<CategoryNetModel>? categories,
    Map<String, dynamic>? metadata,
  }) {
    return BackupNetModel(
      version: version ?? this.version,
      createdAt: createdAt ?? this.createdAt,
      accounts: accounts ?? this.accounts,
      categories: categories ?? this.categories,
    );
  }

  /// 获取备份文件名
  String get fileName {
    return 'password_backup_${createdAt}.json';
  }

  List<CategoryNetModel> get categoriesWithoutNull {
    return categories?.whereType<CategoryNetModel>().toList() ?? List.empty();
  }

  List<AccountNetModel> get accountsWithoutNull {
    return accounts?.whereType<AccountNetModel>().toList() ?? List.empty();
  }
}

/// 备份历史记录模型
class BackupHistory {
  final String id;
  final String fileName;
  final DateTime createdAt;
  final BackupType type;
  final BackupStatus status;
  final String? errorMessage;
  final Map<String, int> statistics;

  const BackupHistory({
    required this.id,
    required this.fileName,
    required this.createdAt,
    required this.type,
    required this.status,
    this.errorMessage,
    this.statistics = const {},
  });

  /// 从JSON创建实例
  factory BackupHistory.fromJson(Map<String, dynamic> json) {
    return BackupHistory(
      id: json['id'],
      fileName: json['fileName'],
      createdAt: DateTime.parse(json['createdAt']),
      type: BackupType.values[json['type'] ?? 0],
      status: BackupStatus.values[json['status'] ?? 0],
      errorMessage: json['errorMessage'],
      statistics: Map<String, int>.from(json['statistics'] ?? {}),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fileName': fileName,
      'createdAt': createdAt.toIso8601String(),
      'type': type.index,
      'status': status.index,
      'errorMessage': errorMessage,
      'statistics': statistics,
    };
  }
}

/// 备份类型
enum BackupType {
  webdav,
  local,
}

/// 备份状态
enum BackupStatus {
  success,
  failed,
  inProgress,
}
