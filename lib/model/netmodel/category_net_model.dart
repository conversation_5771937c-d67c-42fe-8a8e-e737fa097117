import 'package:json_annotation/json_annotation.dart';

import '../../shared/models/category_model.dart';

part 'category_net_model.g.dart';

/// 网络分类数据模型
/// 专门用于网络数据传输和导入
@JsonSerializable()
class CategoryNetModel {
  final String id;
  final String name;
  final String icon;
  final String color;
  final String description;
  final bool isDefault;
  final int order;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const CategoryNetModel({
    required this.id,
    required this.name,
    this.icon = 'category',
    this.color = '#4286EA',
    this.description = '',
    this.isDefault = false,
    this.order = 0,
    this.createdAt,
    this.updatedAt,
  });

  factory CategoryNetModel.fromJson(Map<String, dynamic> json) =>
      _$CategoryNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryNetModelToJson(this);

  /// 转换为本地CategoryModel
  CategoryModel toCategoryModel() {
    return CategoryModel(
      id: id,
      name: name,
      icon: icon,
      color: color,
      description: description,
      isDefault: isDefault,
      order: order,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  /// 从CategoryModel创建NetModel
  factory CategoryNetModel.fromCategoryModel(CategoryModel category) {
    return CategoryNetModel(
      id: category.id,
      name: category.name,
      icon: category.icon,
      color: category.color,
      description: category.description,
      isDefault: category.isDefault,
      order: category.order,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt,
    );
  }

  /// 复制并修改
  CategoryNetModel copyWith({
    String? id,
    String? name,
    String? icon,
    String? color,
    String? description,
    bool? isDefault,
    int? order,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CategoryNetModel(
      id: id ?? this.id,
      name: name ?? this.name,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      description: description ?? this.description,
      isDefault: isDefault ?? this.isDefault,
      order: order ?? this.order,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CategoryNetModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CategoryNetModel{id: $id, name: $name}';
  }
}
