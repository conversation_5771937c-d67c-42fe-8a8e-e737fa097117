import 'package:json_annotation/json_annotation.dart';
import 'package:password/model/netmodel/account_net_model.dart';
import 'package:password/model/otp_parameters_model.dart';
import 'package:uuid/uuid.dart';

part 'otp_net_model.g.dart';

@JsonSerializable()
class OtpParametersNetModel {
  final String? secret;
  final String? name;
  final String? issuer;
  final String? algorithm;
  final int? digits;
  final int? counter;
  final String? type;
  final String? uniqueId;

  OtpParametersNetModel({
    this.secret,
    this.name,
    this.issuer,
    this.algorithm,
    this.digits,
    this.counter,
    this.type,
    this.uniqueId,
  });

  factory OtpParametersNetModel.fromJson(Map<String, dynamic> json) =>
      _$OtpParametersNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$OtpParametersNetModelToJson(this);

  // 从网络实体转换的工厂方法
  asBizModel() {
    final netModel = this;
    return OtpParametersModel(
      secret: netModel.secret ?? '',
      name: netModel.name ?? '',
      issuer: netModel.issuer ?? '',
      algorithm: netModel.algorithm ?? 'SHA1',
      // 默认算法
      digits: netModel.digits ?? 6,
      // 默认位数
      counter: netModel.counter,
      type: _parseOtpType(netModel.type),
      uniqueId: netModel.uniqueId ?? '',
    );
  }

  // 解析OTP类型
  OtpType _parseOtpType(String? type) {
    if (type == null) return OtpType.unknown;

    switch (type.toUpperCase()) {
      case 'TOTP':
        return OtpType.totp;
      case 'HOTP':
        return OtpType.hotp;
      default:
        return OtpType.unknown;
    }
  }

  AccountNetModel toAccountNetModel() {
    return AccountNetModel(
        id: const Uuid().v4(),
        title: name ?? "",
        username: name ?? "",
        password: null,
        otpParameters: this);
  }
}

@JsonSerializable()
class OtpConfigNetModel {
  final int? version;
  final int? batchId;
  final int? batchIndex;
  final int? batchSize;
  @JsonKey(name: 'otpParameters')
  final List<OtpParametersNetModel>? otpParameters;

  OtpConfigNetModel({
    this.version,
    this.batchId,
    this.batchIndex,
    this.batchSize,
    this.otpParameters,
  });

  factory OtpConfigNetModel.fromJson(Map<String, dynamic> json) =>
      _$OtpConfigNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$OtpConfigNetModelToJson(this);
}
