/// 账号密码模型
class AccountModel {
  final String id;
  final String title;
  final String username;
  final String password;
  final String? email;
  final String? website;
  final String? notes;
  final String? categoryId;
  final List<String> tags;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? lastUsed;
  final bool isFavorite;

  const AccountModel({
    required this.id,
    required this.title,
    required this.username,
    required this.password,
    this.email,
    this.website,
    this.notes,
    this.categoryId,
    this.tags = const [],
    this.createdAt,
    this.updatedAt,
    this.lastUsed,
    this.isFavorite = false,
  });

  /// 复制并修改
  AccountModel copyWith({
    String? id,
    String? title,
    String? username,
    String? password,
    String? email,
    String? website,
    String? notes,
    String? categoryId,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastUsed,
    bool? isFavorite,
    // 用于明确设置为null的标志
    bool clearTwoFactorSecret = false,
  }) {
    return AccountModel(
      id: id ?? this.id,
      title: title ?? this.title,
      username: username ?? this.username,
      password: password ?? this.password,
      email: email ?? this.email,
      website: website ?? this.website,
      notes: notes ?? this.notes,
      categoryId: categoryId ?? this.categoryId,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastUsed: lastUsed ?? this.lastUsed,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AccountModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'AccountModel(id: $id, title: $title, username: $username, categoryId: $categoryId)';
  }
}
