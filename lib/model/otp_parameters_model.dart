import 'package:password/model/netmodel/otp_net_model.dart';

// OTP类型枚举
enum OtpType {
  totp,
  hotp,
  unknown;

  // 可选：添加toString方法
  @override
  String toString() {
    switch (this) {
      case OtpType.totp:
        return 'TOTP';
      case OtpType.hotp:
        return 'HOTP';
      case OtpType.unknown:
        return 'UNKNOWN';
    }
  }
}

class OtpParametersModel {
  final String secret;
  final String name;
  final String issuer;
  final String algorithm;
  final int digits;
  final int? counter;
  final OtpType type; // 使用枚举类型更安全
  final String uniqueId;

  OtpParametersModel({
    required this.secret,
    required this.name,
    required this.issuer,
    required this.algorithm,
    required this.digits,
    this.counter,
    required this.type,
    required this.uniqueId,
  });
}
