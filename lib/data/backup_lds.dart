import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';

@injectable
class BackupLds {
  static const String _backupDataKey = 'backup_data';

  final SharedPreferences _prefs;

  BackupLds(this._prefs) {}

  /// 获取当前缓存数据
  String? getBackupData() {
    return _prefs.getString(_backupDataKey);
  }

  Future<void> saveBackupData(String data) async {
    await _prefs.setString(_backupDataKey, data);
  }
}
