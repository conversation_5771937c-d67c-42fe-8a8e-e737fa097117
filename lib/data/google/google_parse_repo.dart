import 'package:injectable/injectable.dart';
import 'package:password/data/google/google_authenticator_exporter.dart';
import 'package:password/model/account_model.dart';
import 'package:password/model/netmodel/otp_net_model.dart';
import 'package:ruolanui/ruolanui.dart';

@injectable
class GoogleParseRepo {
  /// 从Google Authenticator迁移URI解析OTP参数
  Future<Result<OtpConfigNetModel?>> parseGoogleAuthenticatorUri(
      String uri) async {
    try {
      final accounts = GoogleAuthenticatorExporter.exportFromMigrationUri(uri);
      final netModel = OtpConfigNetModel.fromJson(accounts);
      return Result.success(netModel);
    } catch (e) {
      print('EEE:$e');
      return Result.failure(e.toString());
    }
  }

  Future<List<AccountModel>> parseGoogleAuthenticatorData(String data) async {
    final result = await parseGoogleAuthenticatorUri(data);
    final account = result.data?.otpParameters ?? [];
    account.forEach((r) {
      // 当前数据：
      print('"RRRR:${r}');
    });
    // 处理谷歌2fa账号转化为本地的账号数据
    return account.map((e) => e.toAccountNetModel().toAccountModel()).toList();
  }
}
