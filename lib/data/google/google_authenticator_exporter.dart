import 'dart:convert';
import 'dart:typed_data';

import 'package:base32/base32.dart';
import 'package:password/protos/migration.pb.dart';

class GoogleAuthenticatorExporter {
  /// 从迁移URI解析OTP参数
  static Map<String, dynamic> exportFromMigrationUri(String uri) {
    try {
      // 验证URI格式
      if (!uri.startsWith('otpauth-migration://offline?data=')) {
        throw const FormatException(
            'Invalid Google Authenticator migration URI');
      }

      // 提取并处理Base64数据
      final encodedData = _extractDataFromUri(uri);
      final decodedData = _safeBase64Decode(encodedData);

      // 解析Protobuf
      final payload = MigrationPayload.fromBuffer(decodedData);

      return _convertToJson(payload);
    } catch (e) {
      throw FormatException('Failed to parse migration data: ${e.toString()}');
    }
  }

  static String _extractDataFromUri(String uri) {
    final dataPart = uri.split('data=')[1];
    return Uri.decodeComponent(dataPart);
  }

  static Uint8List _safeBase64Decode(String data) {
    // 处理URL安全的Base64
    var normalized = data.replaceAll('-', '+').replaceAll('_', '/');

    // 添加必要的padding
    final padLength = (4 - normalized.length % 4) % 4;
    normalized += '=' * padLength;

    return base64.decode(normalized);
  }

  static Map<String, dynamic> _convertToJson(MigrationPayload payload) {
    return {
      'version': payload.version,
      'batchId': payload.batchId,
      'batchIndex': payload.batchIndex,
      'batchSize': payload.batchSize,
      'otpParameters': payload.otpParameters.map(_convertOtpParameter).toList(),
    };
  }

  static Map<String, dynamic> _convertOtpParameter(
      MigrationPayload_OtpParameters param) {
    return {
      'secret': base32.encode(Uint8List.fromList(param.secret)),
      'name': param.name,
      'issuer': param.issuer,
      'algorithm': _algorithmToString(param.algorithm),
      'digits': _digitsToInt(param.digits),
      'type': _typeToString(param.type),
      'totp_timer':
          param.type == MigrationPayload_OtpType.HOTP ? param.counter : null,
      'uniqueId': param.uniqueId,
    };
  }

  static String _algorithmToString(MigrationPayload_Algorithm algorithm) {
    switch (algorithm) {
      case MigrationPayload_Algorithm.SHA1:
        return 'SHA1';
      case MigrationPayload_Algorithm.SHA256:
        return 'SHA256';
      case MigrationPayload_Algorithm.SHA512:
        return 'SHA512';
      case MigrationPayload_Algorithm.MD5:
        return 'MD5';
      default:
        return 'UNSPECIFIED';
    }
  }

  static int _digitsToInt(MigrationPayload_DigitCount digits) {
    switch (digits) {
      case MigrationPayload_DigitCount.SIX:
        return 6;
      case MigrationPayload_DigitCount.EIGHT:
        return 8;
      case MigrationPayload_DigitCount.SEVEN:
        return 7;
      default:
        return 6; // 默认返回6位
    }
  }

  static String _typeToString(MigrationPayload_OtpType type) {
    switch (type) {
      case MigrationPayload_OtpType.HOTP:
        return 'HOTP';
      case MigrationPayload_OtpType.TOTP:
        return 'TOTP';
      default:
        return 'UNSPECIFIED';
    }
  }
}
