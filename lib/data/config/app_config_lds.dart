import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:password/model/config/app_config.dart';
import 'package:shared_preferences/shared_preferences.dart';

@injectable
class AppConfigLds {
  static const String _app_config_key = 'app_config';

  final SharedPreferences _prefs;

  AppConfigLds(this._prefs) {}

  AppConfig getAppConfig() {
    try {
      final json = _prefs.getString(_app_config_key);
      if (json == null) return AppConfig.defaultConfig();
      return AppConfig.fromJson(jsonDecode(json));
    } catch (e) {
      return AppConfig.defaultConfig();
    }
  }

  setAgreePrivacy(bool agreed) async {
    try {
      print('设置隐私协议状态: $agreed');
      final config = getAppConfig().copyWith(agreePrivacy: agreed);
      final json = jsonEncode(config.toJson());
      await _prefs.setString(_app_config_key, json);
    } catch (e) {
      print('设置隐私协议状态失败: $e');
    }
  }
}
