import 'package:flutter/services.dart';
import 'package:injectable/injectable.dart';
import 'package:password/data/backup_lds.dart';
import 'package:password/gen/assets.gen.dart';
import 'package:password/model/account_model.dart';
import 'package:password/model/netmodel/account_net_model.dart';
import 'package:password/model/netmodel/backup_net_model.dart';
import 'package:password/model/netmodel/category_net_model.dart';
import 'package:password/shared/models/category_model.dart';

@injectable
class BackupRepo {
  final BackupLds lds;

  BackupRepo({required this.lds}) {}

  Future<BackupNetModel> getBackupData() async {
    try {
      final data = lds.getBackupData();
      if (data == null) {
        final jsonString =
            await rootBundle.loadString(Assets.cache.initBackupData);
        await lds.saveBackupData(jsonString);
        return BackupNetModel.fromJsonString(jsonString);
      }
      return BackupNetModel.fromJsonString(data);
    } catch (e) {
      print('获取备份数据失败: $e');
      return BackupNetModel.defaultData();
    }
  }

  BackupNetModel? getBackupDataSync() {
    final data = lds.getBackupData();
    if (data == null) return null;
    return BackupNetModel.fromJsonString(data);
  }

  Future<void> saveBackupData(BackupNetModel data) async {
    final jsonString = data.toJsonString();
    await lds.saveBackupData(jsonString);
  }

  /// 保存账号数据
  Future<void> saveAccountData(List<AccountModel> accounts) async {
    final backupData = getBackupDataSync() ?? BackupNetModel.defaultData();
    final accountNetModels =
        accounts.map((i) => AccountNetModel.fromAccountModel(i)).toList();
    final newData = backupData.copyWith(accounts: accountNetModels);
    await saveBackupData(newData);
  }

  /// 保存分类数据
  Future<void> saveCategoryData(List<CategoryModel> categories) async {
    final backupData = getBackupDataSync() ?? BackupNetModel.defaultData();
    final categoryNetModels =
        categories.map((i) => CategoryNetModel.fromCategoryModel(i)).toList();
    final newData = backupData.copyWith(categories: categoryNetModels);
    await saveBackupData(newData);
  }
}
