import 'package:flutter/material.dart';
import 'dart:async';

class TOTPTimerWidget extends StatefulWidget {
  final int totalTime; // 总时间（默认30秒）
  final ValueChanged<int>? onTick; // 每秒回调
  final VoidCallback? onTimerEnd; // 倒计时结束回调
  final TOTPTimerController? controller; // 控制器
  final Widget Function(BuildContext, int)? builder; // 自定义UI构建

  const TOTPTimerWidget({
    super.key,
    this.totalTime = 30,
    this.onTick,
    this.onTimerEnd,
    this.controller,
    this.builder,
  });

  @override
  State<TOTPTimerWidget> createState() => _TOTPTimerWidgetState();
}

class _TOTPTimerWidgetState extends State<TOTPTimerWidget> {
  late int _remainingTime;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _remainingTime = _calculateInitialRemainingTime();
    _initController();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _initController() {
    widget.controller?._attach(
      refresh: _refreshCode,
      getRemainingTime: () => _remainingTime,
    );
  }

  int _calculateInitialRemainingTime() {
    final now = DateTime.now();
    return widget.totalTime - (now.second % widget.totalTime);
  }

  void _startTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _remainingTime -= 1;
        if (_remainingTime <= 0) {
          _remainingTime = widget.totalTime;
          widget.onTimerEnd?.call();
        }
        widget.onTick?.call(_remainingTime);
      });
    });
  }

  void _refreshCode() {
    setState(() {
      _remainingTime = widget.totalTime;
    });
  }

  @override
  Widget build(BuildContext context) {
    return widget.builder != null
        ? widget.builder!(context, _remainingTime)
        : _defaultBuilder(context);
  }

  Widget _defaultBuilder(BuildContext context) {
    final progressValue = _remainingTime / widget.totalTime;
    final isCritical = _remainingTime <= 5;

    return SizedBox(
      width: 40,
      height: 40,
      child: Stack(
        alignment: Alignment.center,
        children: [
          CircularProgressIndicator(
            value: progressValue,
            strokeWidth: 3,
            backgroundColor: Colors.grey.shade300,
            valueColor: AlwaysStoppedAnimation<Color>(
              isCritical ? Colors.red : Colors.blue,
            ),
          ),
          Text(
            '$_remainingTime',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: isCritical ? Colors.red : Colors.blue,
            ),
          ),
        ],
      ),
    );
  }
}

// 控制器类
class TOTPTimerController {
  VoidCallback? _refresh;
  int Function()? _getRemainingTime;

  void _attach(
      {required VoidCallback refresh,
      required int Function() getRemainingTime}) {
    _refresh = refresh;
    _getRemainingTime = getRemainingTime;
  }

  /// 手动刷新验证码
  void refresh() => _refresh?.call();

  /// 获取当前剩余时间
  int get remainingTime => _getRemainingTime?.call() ?? 0;
}
