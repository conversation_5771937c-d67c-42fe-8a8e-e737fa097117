import 'package:flutter/material.dart';
import 'package:ruolanui/ruolanui.dart';

class PasswordTextField extends StatefulWidget {
  final String hintText;
  final String? value;
  final ValueChanged<String> onChange;
  final TextInputAction? textInputAction;
  final InputBorder? border;
  final Color? fillColor;
  final FocusNode? focusNode;
  final ValueChanged<String>? onSubmitted;
  final bool autoFocus;

  PasswordTextField(
      {super.key,
      required this.hintText,
      required this.onChange,
      this.textInputAction,
      this.value,
      this.fillColor,
      this.focusNode,
      this.onSubmitted,
      this.autoFocus = false,
      InputBorder? border})
      : border = const OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(12)),
          borderSide: BorderSide.none,
        );

  @override
  State<PasswordTextField> createState() => _PasswordTextFieldState();
}

class _PasswordTextFieldState extends State<PasswordTextField> {
  late TextEditingController _controller;

  late FocusNode _focusNode;
  bool obscureText = true;
  bool _showEye = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _controller = TextEditingController();
    _controller.addListener(_onTextChanged);
    _focusNode.addListener(_onFocusChanged);
    _controller.text = widget.value ?? "";
  }

  @override
  void didUpdateWidget(covariant PasswordTextField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.value != _controller.text) {
      _controller.text = widget.value ?? "";
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _controller.dispose();
    _focusNode.removeListener(_onFocusChanged);
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    setState(() {
      _showEye = _controller.text.isNotEmpty && _focusNode.hasFocus;
    });
  }

  void _onFocusChanged() {
    setState(() {
      _showEye = _controller.text.isNotEmpty && _focusNode.hasFocus;
    });
  }

  @override
  Widget build(BuildContext context) {
    return AppTextField(
      autofocus: widget.autoFocus,
      hintText: widget.hintText,
      icon: Icons.lock_outline,
      obscureText: obscureText,
      tailIcon: Visibility(
        visible: _showEye,
        child: IconButton(
          onPressed: () {
            setState(() {
              obscureText = !obscureText;
            });
          },
          icon: obscureText
              ? const Icon(Icons.visibility_off)
              : const Icon(Icons.remove_red_eye),
        ),
      ),
      onChange: widget.onChange,
      focusNode: _focusNode,
      controller: _controller,
      textInputAction: widget.textInputAction,
      border: widget.border,
      fillColor: widget.fillColor,
      onSubmitted: widget.onSubmitted,
    );
  }
}
