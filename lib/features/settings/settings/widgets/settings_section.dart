import 'package:flutter/material.dart';

class SettingsSection extends StatelessWidget {
  final ThemeMode themeMode;
  final VoidCallback onThemeChanged;
  final VoidCallback onCategoryManagement;
  final VoidCallback onTagManagement;
  final VoidCallback onArchiveManagement;
  final VoidCallback onWebdavBackup;
  final VoidCallback onResetPassword;
  final VoidCallback onLockApp;
  final VoidCallback onBiometricSettings;
  final bool isBiometricAvailable;
  final bool isBiometricEnabled;
  final String biometricStatusText;

  const SettingsSection({
    super.key,
    required this.themeMode,
    required this.onThemeChanged,
    required this.onCategoryManagement,
    required this.onTagManagement,
    required this.onArchiveManagement,
    required this.onWebdavBackup,
    required this.onResetPassword,
    required this.onLockApp,
    required this.onBiometricSettings,
    required this.isBiometricAvailable,
    required this.isBiometricEnabled,
    required this.biometricStatusText,
  });

  String _getThemeModeDisplayName(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return '浅色模式';
      case ThemeMode.dark:
        return '深色模式';
      case ThemeMode.system:
        return '跟随系统';
    }
  }

  IconData _getThemeModeIcon(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.brightness_auto;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '应用设置',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildSettingItem(
                icon: _getThemeModeIcon(themeMode),
                iconColor: const Color(0xFF4286EA),
                title: '主题设置',
                subtitle: _getThemeModeDisplayName(themeMode),
                onTap: onThemeChanged,
              ),
              _buildDivider(),
              if (isBiometricAvailable)
                _buildSettingItem(
                  icon: Icons.fingerprint,
                  iconColor: Colors.green,
                  title: '生物识别',
                  subtitle: biometricStatusText,
                  onTap: onBiometricSettings,
                ),
              if (isBiometricAvailable) _buildDivider(),
              _buildSettingItem(
                icon: Icons.category,
                iconColor: Colors.purple,
                title: '分类管理',
                subtitle: '管理账号分类',
                onTap: onCategoryManagement,
              ),
              _buildDivider(),
              _buildSettingItem(
                icon: Icons.local_offer,
                iconColor: Colors.orange,
                title: '标签管理',
                subtitle: '管理账号标签',
                onTap: onTagManagement,
              ),
              _buildDivider(),
              _buildSettingItem(
                icon: Icons.archive,
                iconColor: Colors.green,
                title: '数据归档',
                subtitle: '管理已归档的数据文件',
                onTap: onArchiveManagement,
              ),
              _buildDivider(),
              _buildSettingItem(
                icon: Icons.cloud_sync,
                iconColor: Colors.blue,
                title: 'WebDAV备份',
                subtitle: '备份数据到WebDAV服务器',
                onTap: onWebdavBackup,
              ),
              _buildDivider(),
              _buildSettingItem(
                icon: Icons.refresh,
                iconColor: Colors.red,
                title: '重置主密码',
                subtitle: '重置密码并归档当前数据',
                onTap: onResetPassword,
              ),
              _buildDivider(),
              _buildSettingItem(
                icon: Icons.lock,
                iconColor: Colors.orange,
                title: '锁定应用',
                subtitle: '立即锁定应用',
                onTap: onLockApp,
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),
        const Text(
          '关于',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildSettingItem(
                icon: Icons.info,
                iconColor: const Color(0xFF4286EA),
                title: '应用版本',
                subtitle: '1.0.0',
                onTap: () {},
                showArrow: false,
              ),
              _buildDivider(),
              _buildSettingItem(
                icon: Icons.help,
                iconColor: Colors.purple,
                title: '帮助与反馈',
                subtitle: '获取帮助或提供反馈',
                onTap: () {
                  // TODO: 实现帮助页面
                },
              ),
              _buildDivider(),
              _buildSettingItem(
                icon: Icons.privacy_tip,
                iconColor: Colors.teal,
                title: '隐私政策',
                subtitle: '查看隐私政策',
                onTap: () {
                  // TODO: 实现隐私政策页面
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool showArrow = true,
  }) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: iconColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: iconColor,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey[600],
        ),
      ),
      trailing: showArrow
          ? const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey,
            )
          : null,
      onTap: onTap,
    );
  }

  Widget _buildDivider() {
    return Divider(
      height: 1,
      indent: 72,
      color: Colors.grey[200],
    );
  }
}
