import 'package:flutter/material.dart';
import 'package:ruolanui/ruolanui.dart';

class UserProfileSection extends StatelessWidget {
  final String userName;
  final String? userEmail;
  final String? userAvatar;
  final String userAvatarText;
  final bool isVip;
  final int? vipRemainingDays;
  final Future<bool> Function(String) onUpdateName;
  final Future<bool> Function(String?) onUpdateEmail;
  final Future<bool> Function(String?) onUpdateAvatar;

  const UserProfileSection({
    super.key,
    required this.userName,
    this.userEmail,
    this.userAvatar,
    required this.userAvatarText,
    required this.isVip,
    this.vipRemainingDays,
    required this.onUpdateName,
    required this.onUpdateEmail,
    required this.onUpdateAvatar,
  });

  void _showEditNameDialog(BuildContext context) async {
    final result = await showBottomInputDialog(
      context,
      title: "修改用户名",
      hintText: '请输入用户名',
      initialValue: userName,
    );
    result.onSuccess((r) async {
      if (r != null && r.isNotEmpty) {
        await onUpdateName(r);
      }
    });
  }

  void _showEditEmailDialog(BuildContext context) async {
    final result = await showBottomInputDialog(
      context,
      title: "修改邮箱",
      hintText: '请输入邮箱地址',
      initialValue: userEmail ?? '',
      keyboardType: TextInputType.emailAddress,
    );
    result.onSuccess((r) async {
      await onUpdateEmail(r?.isEmpty == true ? null : r);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 头像和VIP标识
          Row(
            children: [
              Stack(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: const Color(0xFF4286EA),
                    backgroundImage:
                        userAvatar != null ? NetworkImage(userAvatar!) : null,
                    child: userAvatar == null
                        ? Text(
                            userAvatarText,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          )
                        : null,
                  ),
                  if (isVip)
                    Positioned(
                      right: 0,
                      bottom: 0,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: const BoxDecoration(
                          color: Colors.amber,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.star,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          userName,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (isVip) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.amber,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: const Text(
                              'VIP',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    if (userEmail != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        userEmail!,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                    if (isVip && vipRemainingDays != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        vipRemainingDays! > 0
                            ? 'VIP剩余${vipRemainingDays}天'
                            : 'VIP已过期',
                        style: TextStyle(
                          color:
                              vipRemainingDays! > 0 ? Colors.amber : Colors.red,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              IconButton(
                onPressed: () => _showEditNameDialog(context),
                icon: const Icon(Icons.edit),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // 操作按钮
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _showEditEmailDialog(context),
                  icon: const Icon(Icons.email),
                  label: const Text('修改邮箱'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    // TODO: 实现头像选择功能
                  },
                  icon: const Icon(Icons.photo),
                  label: const Text('更换头像'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
