import 'package:flutter/material.dart';
import 'package:mobx/mobx.dart';
import 'package:injectable/injectable.dart';
import '../../../../core/di/injection.dart';
import '../../../../core/stores/app_store.dart';
import '../../../../core/stores/biometric_store.dart';
import '../../../../features/user/stores/user_store.dart';

part 'settings_store.g.dart';

/// 设置页面状态管理
@injectable
class SettingsStore = _SettingsStore with _$SettingsStore;

abstract class _SettingsStore with Store {
  late final AppStore _appStore;
  late final UserStore _userStore;
  late final BiometricStore _biometricStore;

  _SettingsStore() {
    _appStore = getIt<AppStore>();
    _userStore = getIt<UserStore>();
    _biometricStore = getIt<BiometricStore>();
  }

  // Observable状态
  @observable
  bool isLoading = false;

  @observable
  String? errorMessage;

  // Computed values
  @computed
  ThemeMode get themeMode => _appStore.themeMode;

  @computed
  String get userName => _userStore.userName;

  @computed
  String get userAvatarText => _userStore.userAvatarText;

  @computed
  String? get userAvatar => _userStore.userAvatar;

  @computed
  bool get isVip => _userStore.isVip;

  @computed
  int? get vipRemainingDays => _userStore.vipRemainingDays;

  @computed
  String? get userEmail => _userStore.currentUser?.email;

  @computed
  bool get isBiometricAvailable => _biometricStore.isAvailable;

  @computed
  bool get isBiometricEnabled => _biometricStore.isBiometricEnabled;

  @computed
  String get biometricStatusText => _biometricStore.statusText;

  // Actions
  @action
  Future<void> initialize() async {
    isLoading = true;
    errorMessage = null;

    try {
      await _userStore.initialize();
      await _biometricStore.initialize();
    } catch (e) {
      errorMessage = '初始化失败：$e';
    } finally {
      isLoading = false;
    }
  }

  @action
  Future<void> setThemeMode(ThemeMode mode) async {
    await _appStore.changeThemeMode(mode);
  }

  @action
  Future<bool> updateUserName(String name) async {
    isLoading = true;
    errorMessage = null;

    try {
      final success = await _userStore.updateUserName(name);
      if (!success) {
        errorMessage = _userStore.errorMessage ?? '更新用户名失败';
      }
      return success;
    } catch (e) {
      errorMessage = '更新用户名失败：$e';
      return false;
    } finally {
      isLoading = false;
    }
  }

  @action
  Future<bool> updateUserEmail(String? email) async {
    isLoading = true;
    errorMessage = null;

    try {
      final success = await _userStore.updateUserEmail(email);
      if (!success) {
        errorMessage = _userStore.errorMessage ?? '更新邮箱失败';
      }
      return success;
    } catch (e) {
      errorMessage = '更新邮箱失败：$e';
      return false;
    } finally {
      isLoading = false;
    }
  }

  @action
  Future<bool> updateUserAvatar(String? avatar) async {
    isLoading = true;
    errorMessage = null;

    try {
      final success = await _userStore.updateUserAvatar(avatar);
      if (!success) {
        errorMessage = _userStore.errorMessage ?? '更新头像失败';
      }
      return success;
    } catch (e) {
      errorMessage = '更新头像失败：$e';
      return false;
    } finally {
      isLoading = false;
    }
  }

  @action
  Future<bool> resetPasswordWithArchive() async {
    isLoading = true;
    errorMessage = null;

    try {
      final success = await _appStore.resetPasswordWithArchive();
      if (!success) {
        errorMessage = _appStore.errorMessage ?? '重置密码失败';
      }
      return success;
    } catch (e) {
      errorMessage = '重置密码失败：$e';
      return false;
    } finally {
      isLoading = false;
    }
  }

  @action
  void lockApp() {
    _appStore.lock();
  }

  @action
  Future<bool> setBiometricEnabled(bool enabled) async {
    isLoading = true;
    errorMessage = null;

    try {
      final success = await _biometricStore.setBiometricEnabled(enabled);
      if (!success) {
        errorMessage = _biometricStore.errorMessage ?? '设置生物识别失败';
      }
      return success;
    } catch (e) {
      errorMessage = '设置生物识别失败：$e';
      return false;
    } finally {
      isLoading = false;
    }
  }

  @action
  void clearError() {
    errorMessage = null;
    _userStore.clearError();
    _biometricStore.clearError();
  }

  /// 获取归档列表
  Future<List<dynamic>> getArchiveList() async {
    return await _appStore.getArchiveList();
  }

  /// 删除归档
  Future<void> deleteArchive(String fileName) async {
    // TODO: 实现删除归档功能
  }
}
