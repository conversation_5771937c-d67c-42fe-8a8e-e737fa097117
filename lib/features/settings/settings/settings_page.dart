import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/di/injection.dart';

import 'settings_store.dart';
import 'widgets/user_profile_section.dart';
import 'widgets/settings_section.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  late final SettingsStore store;

  @override
  void initState() {
    super.initState();
    store = getIt<SettingsStore>();
    _initializeStore();
  }

  Future<void> _initializeStore() async {
    await store.initialize();
  }

  void _showThemeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择主题'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: ThemeMode.values.map((mode) {
            return Observer(
              builder: (context) {
                return RadioListTile<ThemeMode>(
                  title: Text(_getThemeModeDisplayName(mode)),
                  value: mode,
                  groupValue: store.themeMode,
                  onChanged: (value) {
                    if (value != null) {
                      store.setThemeMode(value);
                      Navigator.of(context).pop();
                    }
                  },
                );
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showBiometricDialog() {
    showDialog(
      context: context,
      builder: (context) => Observer(
        builder: (context) {
          return AlertDialog(
            title: const Text('生物识别设置'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '状态：${store.biometricStatusText}',
                  style: const TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 16),
                if (store.isBiometricAvailable)
                  Column(
                    children: [
                      SwitchListTile(
                        title: const Text('启用生物识别解锁'),
                        subtitle: const Text('使用指纹或人脸识别快速解锁应用'),
                        value: store.isBiometricEnabled,
                        onChanged: store.isLoading
                            ? null
                            : (value) async {
                                print('用户点击生物识别开关: $value');
                                try {
                                  final success =
                                      await store.setBiometricEnabled(value);
                                  print('设置结果: $success');
                                  if (success && mounted) {
                                    Navigator.of(context).pop();
                                  } else if (!success && mounted) {
                                    // 显示详细错误信息
                                    final errorMsg =
                                        store.errorMessage ?? '设置失败，请重试';
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(errorMsg),
                                        backgroundColor: Colors.red,
                                        duration: const Duration(seconds: 3),
                                      ),
                                    );
                                  }
                                } catch (e) {
                                  print('设置生物识别异常: $e');
                                  if (mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text('设置失败：$e'),
                                        backgroundColor: Colors.red,
                                        duration: const Duration(seconds: 3),
                                      ),
                                    );
                                  }
                                }
                              },
                      ),
                      if (store.isLoading)
                        const Padding(
                          padding: EdgeInsets.all(8.0),
                          child: CircularProgressIndicator(),
                        ),
                    ],
                  )
                else
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '您的设备不支持生物识别功能，或者您尚未在系统设置中注册指纹或人脸。',
                        style: TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                      const SizedBox(height: 12),
                      const Text(
                        '要使用生物识别功能，请：',
                        style: TextStyle(
                            fontSize: 14, fontWeight: FontWeight.w500),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        '1. 确保设备支持指纹或人脸识别\n2. 在系统设置中注册指纹或人脸\n3. 重新打开此设置页面',
                        style: TextStyle(fontSize: 13, color: Colors.grey),
                      ),
                    ],
                  ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('关闭'),
              ),
            ],
          );
        },
      ),
    );
  }

  void _showArchiveDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('数据归档管理'),
        content: const Text('查看和管理已归档的数据文件'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final archives = await store.getArchiveList();
              if (mounted) {
                _showArchiveList(archives);
              }
            },
            child: const Text('查看归档'),
          ),
        ],
      ),
    );
  }

  void _showArchiveList(List<dynamic> archives) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('归档文件列表'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: archives.isEmpty
              ? const Center(child: Text('暂无归档文件'))
              : ListView.builder(
                  itemCount: archives.length,
                  itemBuilder: (context, index) {
                    final archive = archives[index];
                    return ListTile(
                      leading: const Icon(Icons.archive),
                      title: Text('归档 ${index + 1}'),
                      subtitle: Text(archive.toString()),
                      trailing: IconButton(
                        icon: const Icon(Icons.delete, color: Colors.red),
                        onPressed: () async {
                          final confirmed = await _showDeleteConfirmDialog();
                          if (confirmed == true) {
                            await store.deleteArchive(archive.toString());
                            Navigator.of(context).pop();
                            final newArchives = await store.getArchiveList();
                            if (mounted) {
                              _showArchiveList(newArchives);
                            }
                          }
                        },
                      ),
                    );
                  },
                ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Future<bool?> _showDeleteConfirmDialog() {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除归档'),
        content: const Text('确定要删除这个归档文件吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.red),
            SizedBox(width: 8),
            Text('重置主密码'),
          ],
        ),
        content: const Text(
          '重置主密码将：\n\n'
          '1. 归档当前所有数据到本地文件\n'
          '2. 清除当前主密码设置\n'
          '3. 需要重新设置新的主密码\n\n'
          '此操作不可撤销，确定要继续吗？',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final success = await store.resetPasswordWithArchive();
              if (success && mounted) {
                context.go('/');
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('确认重置'),
          ),
        ],
      ),
    );
  }

  String _getThemeModeDisplayName(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return '浅色模式';
      case ThemeMode.dark:
        return '深色模式';
      case ThemeMode.system:
        return '跟随系统';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('设置'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: Observer(
        builder: (context) {
          if (store.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (store.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    store.errorMessage!,
                    style: const TextStyle(color: Colors.red),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      store.clearError();
                      _initializeStore();
                    },
                    child: const Text('重试'),
                  ),
                ],
              ),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                UserProfileSection(
                  userName: store.userName,
                  userEmail: store.userEmail,
                  userAvatar: store.userAvatar,
                  userAvatarText: store.userAvatarText,
                  isVip: store.isVip,
                  vipRemainingDays: store.vipRemainingDays,
                  onUpdateName: store.updateUserName,
                  onUpdateEmail: store.updateUserEmail,
                  onUpdateAvatar: store.updateUserAvatar,
                ),
                const SizedBox(height: 24),
                SettingsSection(
                  themeMode: store.themeMode,
                  onThemeChanged: _showThemeDialog,
                  onCategoryManagement: () => context.push('/categories'),
                  onTagManagement: () => context.push('/tags'),
                  onArchiveManagement: _showArchiveDialog,
                  onWebdavBackup: () => context.push('/backup'),
                  onResetPassword: _showResetDialog,
                  onLockApp: store.lockApp,
                  onBiometricSettings: _showBiometricDialog,
                  isBiometricAvailable: store.isBiometricAvailable,
                  isBiometricEnabled: store.isBiometricEnabled,
                  biometricStatusText: store.biometricStatusText,
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
