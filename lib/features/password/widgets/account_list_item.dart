import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../model/account_model.dart';
import '../../../shared/models/category_model.dart';
import '../../../shared/utils/toast_utils.dart';

/// 账号列表项组件
class AccountListItem extends StatelessWidget {
  final AccountModel account;
  final CategoryModel? category;
  final VoidCallback? onTap;
  final VoidCallback? onFavoriteToggle;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const AccountListItem({
    super.key,
    required this.account,
    this.category,
    this.onTap,
    this.onFavoriteToggle,
    this.onEdit,
    this.onDelete,
  });

  Color _getCategoryColor() {
    if (category?.color != null) {
      try {
        return Color(int.parse(category!.color.replaceFirst('#', '0xFF')));
      } catch (e) {
        return const Color(0xFF4286EA);
      }
    }
    return const Color(0xFF4286EA);
  }

  IconData _getCategoryIcon() {
    if (category?.icon != null) {
      switch (category!.icon) {
        case 'group':
          return Icons.group;
        case 'email':
          return Icons.email;
        case 'account_balance':
          return Icons.account_balance;
        case 'shopping_cart':
          return Icons.shopping_cart;
        case 'movie':
          return Icons.movie;
        case 'work':
          return Icons.work;
        case 'school':
          return Icons.school;
        case 'flight':
          return Icons.flight;
        case 'local_hospital':
          return Icons.local_hospital;
        case 'more_horiz':
          return Icons.more_horiz;
        default:
          return Icons.account_circle;
      }
    }
    return Icons.account_circle;
  }

  String _getTimeAgo(DateTime? dateTime) {
    if (dateTime == null) return '从未使用';
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 头部：图标、标题、收藏按钮
              Row(
                children: [
                  // 分类图标
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: _getCategoryColor().withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getCategoryIcon(),
                      color: _getCategoryColor(),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),

                  // 标题和分类
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          account.title,
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (category != null) ...[
                          const SizedBox(height: 2),
                          Text(
                            category!.name,
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: _getCategoryColor(),
                                    ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  // 收藏按钮
                  IconButton(
                    onPressed: onFavoriteToggle,
                    icon: Icon(
                      account.isFavorite
                          ? Icons.favorite
                          : Icons.favorite_border,
                      color: account.isFavorite ? Colors.red : Colors.grey,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // 用户名和邮箱
              Row(
                children: [
                  Icon(
                    Icons.person_outline,
                    size: 16,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      account.username,
                      style: Theme.of(context).textTheme.bodyMedium,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  // 复制用户名按钮
                  IconButton(
                    onPressed: () {
                      Clipboard.setData(ClipboardData(text: account.username));
                      ToastUtils.showSuccess('用户名已复制');
                    },
                    icon: const Icon(Icons.copy, size: 16),
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                  ),
                ],
              ),

              if (account.email != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.email_outlined,
                      size: 16,
                      color: Colors.grey.shade600,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        account.email!,
                        style: Theme.of(context).textTheme.bodySmall,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],

              const SizedBox(height: 12),

              // 底部：时间信息和操作按钮
              Row(
                children: [
                  // 时间信息
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '更新于 ${_getTimeAgo(account.updatedAt)}',
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Colors.grey.shade600,
                                  ),
                        ),
                        if (account.lastUsed != null) ...[
                          const SizedBox(height: 2),
                          Text(
                            '使用于 ${_getTimeAgo(account.lastUsed!)}',
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Colors.grey.shade500,
                                    ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  // 操作按钮
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 复制密码按钮
                      IconButton(
                        onPressed: () {
                          Clipboard.setData(
                              ClipboardData(text: account.password));
                          ToastUtils.showSuccess('密码已复制');
                        },
                        icon: const Icon(Icons.key, size: 18),
                        tooltip: '复制密码',
                      ),

                      // 编辑按钮
                      IconButton(
                        onPressed: onEdit,
                        icon: const Icon(Icons.edit_outlined, size: 18),
                        tooltip: '编辑',
                      ),

                      // 删除按钮
                      IconButton(
                        onPressed: onDelete,
                        icon: const Icon(Icons.delete_outline,
                            size: 18, color: Colors.red),
                        tooltip: '删除',
                      ),
                    ],
                  ),
                ],
              ),

              // 标签
              if (account.tags.isNotEmpty) ...[
                const SizedBox(height: 8),
                Wrap(
                  spacing: 4,
                  runSpacing: 4,
                  children: account.tags.map((tag) {
                    return Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade200,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        tag,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    );
                  }).toList(),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
