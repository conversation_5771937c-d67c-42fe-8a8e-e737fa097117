import 'dart:math';
import 'package:flutter/material.dart';

/// 密码生成配置
class PasswordGeneratorConfig {
  final int length;
  final bool includeUppercase;
  final bool includeLowercase;
  final bool includeNumbers;
  final bool includeSymbols;
  final String customSymbols;
  final bool excludeSimilar;
  final bool excludeAmbiguous;

  const PasswordGeneratorConfig({
    this.length = 12,
    this.includeUppercase = true,
    this.includeLowercase = true,
    this.includeNumbers = true,
    this.includeSymbols = true,
    this.customSymbols = '!@#\$%^&*()_+-=[]{}|;:,.<>?',
    this.excludeSimilar = false,
    this.excludeAmbiguous = false,
  });

  PasswordGeneratorConfig copyWith({
    int? length,
    bool? includeUppercase,
    bool? includeLowercase,
    bool? includeNumbers,
    bool? includeSymbols,
    String? customSymbols,
    bool? excludeSimilar,
    bool? excludeAmbiguous,
  }) {
    return PasswordGeneratorConfig(
      length: length ?? this.length,
      includeUppercase: includeUppercase ?? this.includeUppercase,
      includeLowercase: includeLowercase ?? this.includeLowercase,
      includeNumbers: includeNumbers ?? this.includeNumbers,
      includeSymbols: includeSymbols ?? this.includeSymbols,
      customSymbols: customSymbols ?? this.customSymbols,
      excludeSimilar: excludeSimilar ?? this.excludeSimilar,
      excludeAmbiguous: excludeAmbiguous ?? this.excludeAmbiguous,
    );
  }
}

/// 密码生成器服务
class PasswordGeneratorService {
  static const String _uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  static const String _lowercase = 'abcdefghijklmnopqrstuvwxyz';
  static const String _numbers = '0123456789';
  static const String _defaultSymbols = '!@#\$%^&*()_+-=[]{}|;:,.<>?';

  // 相似字符
  static const String _similarChars = 'il1Lo0O';
  // 模糊字符
  static const String _ambiguousChars = '{}[]()/\\\'"`~,;.<>';

  static final Random _random = Random.secure();

  /// 生成密码
  static String generatePassword(PasswordGeneratorConfig config) {
    if (config.length <= 0) {
      throw ArgumentError('密码长度必须大于0');
    }

    String charset = _buildCharset(config);

    if (charset.isEmpty) {
      throw ArgumentError('至少需要选择一种字符类型');
    }

    // 确保至少包含每种选中的字符类型
    String password = _ensureCharacterTypes(config);

    // 填充剩余长度
    for (int i = password.length; i < config.length; i++) {
      password += charset[_random.nextInt(charset.length)];
    }

    // 打乱密码字符顺序
    List<String> chars = password.split('');
    chars.shuffle(_random);

    return chars.join('');
  }

  /// 构建字符集
  static String _buildCharset(PasswordGeneratorConfig config) {
    String charset = '';

    if (config.includeUppercase) {
      charset += _filterChars(_uppercase, config);
    }

    if (config.includeLowercase) {
      charset += _filterChars(_lowercase, config);
    }

    if (config.includeNumbers) {
      charset += _filterChars(_numbers, config);
    }

    if (config.includeSymbols) {
      charset += _filterChars(config.customSymbols, config);
    }

    return charset;
  }

  /// 过滤字符
  static String _filterChars(String chars, PasswordGeneratorConfig config) {
    String filtered = chars;

    if (config.excludeSimilar) {
      filtered = filtered
          .split('')
          .where((char) => !_similarChars.contains(char))
          .join('');
    }

    if (config.excludeAmbiguous) {
      filtered = filtered
          .split('')
          .where((char) => !_ambiguousChars.contains(char))
          .join('');
    }

    return filtered;
  }

  /// 确保包含每种字符类型
  static String _ensureCharacterTypes(PasswordGeneratorConfig config) {
    String password = '';

    if (config.includeUppercase) {
      String chars = _filterChars(_uppercase, config);
      if (chars.isNotEmpty) {
        password += chars[_random.nextInt(chars.length)];
      }
    }

    if (config.includeLowercase) {
      String chars = _filterChars(_lowercase, config);
      if (chars.isNotEmpty) {
        password += chars[_random.nextInt(chars.length)];
      }
    }

    if (config.includeNumbers) {
      String chars = _filterChars(_numbers, config);
      if (chars.isNotEmpty) {
        password += chars[_random.nextInt(chars.length)];
      }
    }

    if (config.includeSymbols) {
      String chars = _filterChars(config.customSymbols, config);
      if (chars.isNotEmpty) {
        password += chars[_random.nextInt(chars.length)];
      }
    }

    return password;
  }

  /// 评估密码强度
  static PasswordStrength evaluateStrength(String password) {
    if (password.isEmpty) return PasswordStrength.veryWeak;

    int score = 0;

    // 长度评分
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;
    if (password.length >= 16) score += 1;

    // 字符类型评分
    if (password.contains(RegExp(r'[a-z]'))) score += 1;
    if (password.contains(RegExp(r'[A-Z]'))) score += 1;
    if (password.contains(RegExp(r'[0-9]'))) score += 1;
    if (password.contains(RegExp(r'[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]')))
      score += 1;

    // 复杂度评分
    if (password.length > 10 && _hasVariedCharacters(password)) score += 1;

    switch (score) {
      case 0:
      case 1:
      case 2:
        return PasswordStrength.veryWeak;
      case 3:
      case 4:
        return PasswordStrength.weak;
      case 5:
      case 6:
        return PasswordStrength.medium;
      case 7:
        return PasswordStrength.strong;
      default:
        return PasswordStrength.veryStrong;
    }
  }

  /// 检查字符多样性
  static bool _hasVariedCharacters(String password) {
    Set<String> chars = password.split('').toSet();
    return chars.length >= password.length * 0.6;
  }

  /// 获取默认配置
  static PasswordGeneratorConfig getDefaultConfig() {
    return const PasswordGeneratorConfig();
  }

  /// 获取预设配置
  static List<PasswordGeneratorConfig> getPresetConfigs() {
    return [
      const PasswordGeneratorConfig(
        length: 8,
        includeSymbols: false,
      ),
      const PasswordGeneratorConfig(
        length: 12,
      ),
      const PasswordGeneratorConfig(
        length: 16,
        excludeSimilar: true,
      ),
      const PasswordGeneratorConfig(
        length: 20,
        excludeSimilar: true,
        excludeAmbiguous: true,
      ),
    ];
  }
}

/// 密码强度枚举
enum PasswordStrength {
  veryWeak,
  weak,
  medium,
  strong,
  veryStrong,
}

/// 密码强度扩展
extension PasswordStrengthExtension on PasswordStrength {
  String get displayName {
    switch (this) {
      case PasswordStrength.veryWeak:
        return '非常弱';
      case PasswordStrength.weak:
        return '弱';
      case PasswordStrength.medium:
        return '中等';
      case PasswordStrength.strong:
        return '强';
      case PasswordStrength.veryStrong:
        return '非常强';
    }
  }

  Color get color {
    switch (this) {
      case PasswordStrength.veryWeak:
        return const Color(0xFFE53E3E);
      case PasswordStrength.weak:
        return const Color(0xFFFF8C00);
      case PasswordStrength.medium:
        return const Color(0xFFFFD700);
      case PasswordStrength.strong:
        return const Color(0xFF38A169);
      case PasswordStrength.veryStrong:
        return const Color(0xFF00C851);
    }
  }
}
