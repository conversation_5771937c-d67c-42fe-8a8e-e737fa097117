import 'package:mobx/mobx.dart';
import 'package:injectable/injectable.dart';
import 'package:password/features/category/category_manage/category_manage_store.dart';
import 'package:password/features/password/services/password_generator_service.dart';
import 'package:password/features/password/stores/account_store.dart';
import 'package:password/features/tags/tag_manage/tag_manage_store.dart';
import '../../../model/account_model.dart';
import '../../../../shared/models/category_model.dart';
import '../../../../shared/utils/toast_utils.dart';
import '../../../../services/two_factor_service.dart';

part 'account_edit_store.g.dart';

/// 账号编辑页面状态管理
@injectable
class AccountEditStore = _AccountEditStore with _$AccountEditStore;

abstract class _AccountEditStore with Store {
  final AccountStore _accountStore;
  final CategoryManageStore _categoryStore;
  final TagManageStore _tagStore;

  _AccountEditStore(this._accountStore, this._categoryStore, this._tagStore);

  // Observable状态
  @observable
  bool isLoading = false;

  @observable
  bool isSaving = false;

  @observable
  String? errorMessage;

  @observable
  String accountName = '';

  @observable
  String username = '';

  @observable
  String password = '';

  @observable
  String email = '';

  @observable
  String website = '';

  @observable
  String notes = '';

  @observable
  String tags = '';

  @observable
  String? selectedCategoryId;

  @observable
  AccountModel? originalAccount;

  @observable
  bool twoFactorEnabled = false;

  @observable
  String twoFactorSecret = '';

  // Computed values
  @computed
  List<CategoryModel> get categories => _categoryStore.categories.toList();

  @computed
  CategoryModel? get selectedCategory {
    if (selectedCategoryId == null) return null;
    return _categoryStore.getCategoryById(selectedCategoryId!);
  }

  @computed
  PasswordStrength get passwordStrength {
    return PasswordGeneratorService.evaluateStrength(password);
  }

  @computed
  bool get isEditing => originalAccount != null;

  // Actions
  @action
  Future<void> initialize(String? accountId) async {
    isLoading = true;
    errorMessage = null;

    try {
      await _categoryStore.initialize();
      await _accountStore.initialize();

      if (accountId != null) {
        // 编辑模式
        originalAccount = _accountStore.getAccountById(accountId);
        if (originalAccount == null) {
          throw Exception('账号不存在');
        }

        accountName = originalAccount!.title;
        username = originalAccount!.username;
        password = originalAccount!.password;
        email = originalAccount!.email ?? '';
        website = originalAccount!.website ?? '';
        notes = originalAccount!.notes ?? '';
        tags = originalAccount!.tags.join(', ');
        selectedCategoryId = originalAccount!.categoryId;
      } else {
        // 添加模式
        originalAccount = null;
        accountName = '';
        username = '';
        password = '';
        email = '';
        website = '';
        notes = '';
        tags = '';
        twoFactorEnabled = false;
        twoFactorSecret = '';

        // 默认选择第一个分类
        if (categories.isNotEmpty) {
          selectedCategoryId = categories.first.id;
        }
      }
    } catch (e) {
      errorMessage = '初始化失败：$e';
    } finally {
      isLoading = false;
    }
  }

  @action
  void setAccountName(String value) {
    accountName = value;
  }

  @action
  void setUsername(String value) {
    username = value;
  }

  @action
  void setPassword(String value) {
    password = value;
  }

  @action
  void setEmail(String value) {
    email = value;
  }

  @action
  void setWebsite(String value) {
    website = value;
  }

  @action
  void setNotes(String value) {
    notes = value;
  }

  @action
  void setTags(String value) {
    tags = value;
  }

  @action
  void setSelectedCategoryId(String? value) {
    selectedCategoryId = value;
  }

  @action
  void setTwoFactorEnabled(bool value) {
    twoFactorEnabled = value;
  }

  @action
  void setTwoFactorSecret(String value) {
    twoFactorSecret = value;
  }

  @action
  void generateTwoFactorSecret() {
    twoFactorSecret = TwoFactorService.generateSecret();
  }

  @computed
  bool get isTwoFactorSecretValid {
    return twoFactorSecret.trim().isNotEmpty &&
        TwoFactorService.isValidSecret(twoFactorSecret.trim());
  }

  @action
  Future<bool> saveAccount() async {
    if (accountName.isEmpty) {
      errorMessage = "请输入账号名称";
      return false;
    }
    if (username.isEmpty) {
      errorMessage = "请输入用户名";
      return false;
    }

    isSaving = true;
    errorMessage = null;

    try {
      final now = DateTime.now();
      final account = AccountModel(
        id: originalAccount?.id ?? now.millisecondsSinceEpoch.toString(),
        title: accountName.trim(),
        username: username.trim(),
        password: password,
        email: email.trim().isEmpty ? null : email.trim(),
        website: website.trim().isEmpty ? null : website.trim(),
        notes: notes.trim().isEmpty ? null : notes.trim(),
        categoryId: selectedCategoryId,
        tags: _parseTags(tags),
        createdAt: originalAccount?.createdAt ?? now,
        updatedAt: now,
        lastUsed: originalAccount?.lastUsed,
        isFavorite: originalAccount?.isFavorite ?? false,
      );

      bool success;
      if (isEditing) {
        success = await _accountStore.updateAccount(account);
      } else {
        success = await _accountStore.addAccount(account);
      }

      // 如果保存成功，更新标签使用统计
      if (success) {
        final tagNames = _parseTags(tags);
        if (tagNames.isNotEmpty) {
          await _tagStore.incrementTagsUsage(tagNames);
        }
      }

      return success;
    } catch (e) {
      errorMessage = '保存失败：$e';
      ToastUtils.showError('保存失败');
      return false;
    } finally {
      isSaving = false;
    }
  }

  @action
  void clearError() {
    errorMessage = null;
  }

  List<String> _parseTags(String tagsString) {
    if (tagsString.trim().isEmpty) return [];
    return tagsString
        .split(',')
        .map((tag) => tag.trim())
        .where((tag) => tag.isNotEmpty)
        .toList();
  }

  void dispose() {
    // 清理资源
  }
}
