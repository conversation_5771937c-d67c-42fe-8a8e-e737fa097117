import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:mobx/mobx.dart';
import 'package:password/features/password/services/password_generator_service.dart';
import 'package:password/features/tags/tag_manage/tag_manage_store.dart';
import 'package:ruolanui/ruolanui.dart';
import '../../../../core/di/injection.dart';
import '../../../../shared/utils/toast_utils.dart';
import '../../../../shared/widgets/password_generator_bottom_sheet.dart';
import '../../../../shared/widgets/password_input_bottom_sheet.dart';

import '../../../../shared/widgets/two_factor_config_bottom_sheet.dart';
import 'account_edit_store.dart';
import '../../../../shared/widgets/item_select_form.dart';
import '../../../../shared/widgets/category_grid_select_sheet.dart';

class AccountEditPage extends StatefulWidget {
  final String? accountId;

  const AccountEditPage({
    super.key,
    this.accountId,
  });

  @override
  State<AccountEditPage> createState() => _AccountEditPageState();
}

class _AccountEditPageState extends State<AccountEditPage> {
  TextTheme get textTheme => Theme.of(context).textTheme;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  late final AccountEditStore store;
  late final TagManageStore tagStore;

  @override
  void initState() {
    super.initState();
    store = getIt<AccountEditStore>();
    tagStore = getIt<TagManageStore>();
    _initializeStore();
    initEvent();
  }

  void initEvent() {
    reaction((_) => store.errorMessage, (e) {
      if (e == null) return;
      store.clearError();
      ToastUtils.showError(e ?? "");
    });
  }

  Future<void> _initializeStore() async {
    await store.initialize(widget.accountId);
  }

  @override
  void dispose() {
    store.dispose();
    super.dispose();
  }

  Future<void> _handleSave() async {
    final success = await store.saveAccount();
    if (success && mounted) {
      context.pop();
    }
  }

  void _showAccountNameDialog() async {
    final result = await showBottomInputDialog(
      context,
      title: "账号名称",
      hintText: '例如：微信、QQ、支付宝',
      initialValue: store.accountName,
    );
    result.onSuccess((r) {
      store.setAccountName(r ?? "");
    });
  }

  void _showUsernameDialog() async {
    final result = await showBottomInputDialog(
      context,
      title: "用户名",
      hintText: '请输入用户名',
      initialValue: store.username,
    );
    result.onSuccess((r) {
      store.setUsername(r ?? "");
    });
  }

  void _showPasswordDialog() async {
    final result = await showPasswordInputBottomSheet(
      context,
      initialValue: store.password,
    );

    if (result != null) {
      store.setPassword(result);
    }
  }

  void _showEmailDialog() async {
    final result = await showBottomInputDialog(
      context,
      title: "邮箱",
      hintText: '请输入邮箱地址（可选）',
      initialValue: store.email,
      keyboardType: TextInputType.emailAddress,
    );
    result.onSuccess((r) {
      store.setEmail(r ?? "");
    });
  }

  void _showWebsiteDialog() async {
    final result = await showBottomInputDialog(
      context,
      title: "网站",
      hintText: '请输入网站地址（可选）',
      initialValue: store.website,
      keyboardType: TextInputType.url,
    );
    result.onSuccess((r) {
      store.setWebsite(r ?? "");
    });
  }

  void _showNotesDialog() async {
    final result = await showBottomInputDialog(
      context,
      title: "备注",
      hintText: '请输入备注信息（可选）',
      initialValue: store.notes,
    );
    result.onSuccess((r) {
      store.setNotes(r ?? "");
    });
  }

  void _showTwoFactorConfigDialog() async {
    final result = await showModalBottomSheet<Map<String, dynamic>>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: TwoFactorConfigBottomSheet(
          initialSecret: store.twoFactorSecret,
          initialEnabled: store.twoFactorEnabled,
          accountName: store.accountName.isNotEmpty ? store.accountName : '账号',
        ),
      ),
    );

    if (result != null) {
      store.setTwoFactorEnabled(result['enabled'] ?? false);
      store.setTwoFactorSecret(result['secret'] ?? '');
    }
  }

  void _showTagsDialog() async {
    // 将当前标签字符串转换为标签列表
    final currentTags = store.tags.isEmpty
        ? <String>[]
        : store.tags
            .split(',')
            .map((tag) => tag.trim())
            .where((tag) => tag.isNotEmpty)
            .toList();

    final result = await context.pushNamed(
      'tag-selection',
      extra: currentTags,
    );

    if (result != null && result is List<String>) {
      // 将选择的标签列表转换为逗号分隔的字符串
      store.setTags(result.join(', '));
    }
  }

  void _showCategoryDialog() async {
    final result = await showCategoryGridSelect(
      context,
      selectedCategory: store.selectedCategory,
      categories: store.categories,
    );

    if (result != null) {
      store.setSelectedCategoryId(result.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CommonAppBar(
        title: widget.accountId == null ? '添加账号' : '编辑账号',
        actions: [
          TextButton(
              onPressed: _handleSave,
              child: Text(
                "保存",
                style:
                    textTheme.titleMedium!.copyWith(color: colorScheme.primary),
              ))
        ],
      ),
      body: Observer(
        builder: (context) {
          if (store.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // 基本信息
                _buildSectionTitle('基本信息'),
                const SizedBox(height: 12),
                ItemSelectForm(
                  title: '账号名称',
                  required: true,
                  hintText: '请输入账号名称',
                  value: store.accountName,
                  onTap: _showAccountNameDialog,
                ),
                const SizedBox(height: 12),
                ItemSelectForm(
                  title: '用户名',
                  required: true,
                  hintText: '请输入用户名',
                  value: store.username,
                  onTap: _showUsernameDialog,
                ),
                const SizedBox(height: 12),
                ItemSelectForm(
                  title: '密码',
                  hintText: '请输入密码',
                  value: store.password,
                  onTap: _showPasswordDialog,
                ),

                const SizedBox(height: 24),

                // 分类信息
                _buildSectionTitle('分类'),
                const SizedBox(height: 12),
                ItemSelectForm(
                  title: '分类',
                  hintText: '请选择分类',
                  value: store.selectedCategory?.name ?? '',
                  onTap: _showCategoryDialog,
                ),

                const SizedBox(height: 24),

                // 可选信息
                _buildSectionTitle('可选信息'),
                const SizedBox(height: 12),
                ItemSelectForm(
                  title: '邮箱',
                  hintText: '请输入邮箱',
                  value: store.email,
                  onTap: _showEmailDialog,
                ),
                const SizedBox(height: 12),
                ItemSelectForm(
                  title: '网站',
                  hintText: '请输入网站（可选）',
                  value: store.website,
                  onTap: _showWebsiteDialog,
                ),
                const SizedBox(height: 12),
                _buildTagsInfoBlock(),
                const SizedBox(height: 12),
                ItemSelectForm(
                  title: '备注',
                  hintText: '请输入备注（可选）',
                  value: store.notes,
                  onTap: _showNotesDialog,
                ),

                const SizedBox(height: 24),

                // 安全设置
                _buildSectionTitle('安全设置'),
                const SizedBox(height: 12),
                ItemSelectForm(
                  title: '双因素认证 (2FA)',
                  hintText: '点击配置2FA（可选）',
                  value: store.twoFactorEnabled ? '已启用' : '',
                  onTap: _showTwoFactorConfigDialog,
                ),

                const SizedBox(height: 32),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildTagsInfoBlock() {
    return Observer(
      builder: (context) {
        final tagsList = store.tags.isEmpty
            ? <String>[]
            : store.tags
                .split(',')
                .map((tag) => tag.trim())
                .where((tag) => tag.isNotEmpty)
                .toList();

        return Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainer,
            borderRadius: BorderRadius.circular(8),
          ),
          child: InkWell(
            onTap: _showTagsDialog,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    '标签',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(width: 4),
                  const Spacer(),
                  const SizedBox(width: 6),

                  // 标签显示
                  Expanded(
                    child: tagsList.isEmpty
                        ? Text(
                            '请选择标签（可选）',
                            style: Theme.of(context).textTheme.bodyMedium,
                            textAlign: TextAlign.right,
                          )
                        : Wrap(
                            alignment: WrapAlignment.end,
                            spacing: 4,
                            runSpacing: 4,
                            children: tagsList.take(3).map((tagName) {
                              final tagModel = tagStore.getTagByName(tagName);
                              final color = tagModel?.color ?? '#4ECDC4';
                              return Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: _hexToColor(color),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  tagName,
                                  style: const TextStyle(
                                    fontSize: 10,
                                    color: Colors.white,
                                  ),
                                ),
                              );
                            }).toList()
                              ..addAll(tagsList.length > 3
                                  ? [
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 6,
                                          vertical: 2,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.grey.shade400,
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: Text(
                                          '+${tagsList.length - 3}',
                                          style: const TextStyle(
                                            fontSize: 10,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ),
                                    ]
                                  : []),
                          ),
                  ),

                  const SizedBox(width: 6),
                  const Icon(
                    Icons.chevron_right,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSectionTitle(String title) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
      ),
    );
  }

  Color _hexToColor(String hex) {
    hex = hex.replaceAll('#', '');
    if (hex.length == 6) {
      hex = 'FF$hex';
    }
    return Color(int.parse(hex, radix: 16));
  }
}
