import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/di/injection.dart';
import '../../../../shared/utils/color_utils.dart';

import 'account_detail_store.dart';

class AccountDetailPage extends StatefulWidget {
  final String accountId;

  const AccountDetailPage({
    super.key,
    required this.accountId,
  });

  @override
  State<AccountDetailPage> createState() => _AccountDetailPageState();
}

class _AccountDetailPageState extends State<AccountDetailPage> {
  late final AccountDetailStore store;

  @override
  void initState() {
    super.initState();
    store = getIt<AccountDetailStore>();
    _initializeStore();
  }

  Future<void> _initializeStore() async {
    await store.initialize(widget.accountId);
  }

  @override
  void dispose() {
    store.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('账号详情'),
        actions: [
          Observer(
            builder: (context) {
              if (!store.hasAccount) return const SizedBox.shrink();

              return PopupMenuButton<String>(
                onSelected: _handleMenuAction,
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        const Icon(Icons.edit, size: 16),
                        const SizedBox(width: 8),
                        const Text('编辑'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'favorite',
                    child: Row(
                      children: [
                        Icon(
                          store.isFavorite
                              ? Icons.favorite
                              : Icons.favorite_border,
                          size: 16,
                          color: store.isFavorite ? Colors.red : null,
                        ),
                        const SizedBox(width: 8),
                        Text(store.isFavorite ? '取消收藏' : '添加收藏'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 16, color: Colors.red),
                        SizedBox(width: 8),
                        Text('删除', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
      body: Observer(
        builder: (context) {
          if (store.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (store.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red.shade300,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    store.errorMessage!,
                    style: TextStyle(
                      color: Colors.red.shade600,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => store.initialize(widget.accountId),
                    child: const Text('重试'),
                  ),
                ],
              ),
            );
          }

          if (!store.hasAccount) {
            return const Center(
              child: Text('账号不存在'),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildAccountHeader(),
                const SizedBox(height: 24),
                _buildAccountInfo(),
                const SizedBox(height: 24),
                _buildPasswordSection(),
                const SizedBox(height: 24),
                _buildTwoFactorSection(),
                const SizedBox(height: 24),
                _buildAdditionalInfo(),
                const SizedBox(height: 24),
                _buildMetaInfo(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildAccountHeader() {
    return Observer(
      builder: (context) {
        final account = store.account;
        final categoryColor = ColorUtils.hexToColor(store.categoryColor);

        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                categoryColor.withOpacity(0.1),
                categoryColor.withOpacity(0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: categoryColor.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: categoryColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _getIconData(store.categoryIcon),
                  color: categoryColor,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      account?.title ?? "",
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      store.categoryName,
                      style: TextStyle(
                        fontSize: 14,
                        color: categoryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (account != null && account.tags.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 4,
                        runSpacing: 4,
                        children: account.tags
                            .map((tag) => Chip(
                                  label: Text(
                                    tag,
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                  backgroundColor:
                                      categoryColor.withOpacity(0.1),
                                  side: BorderSide(
                                    color: categoryColor.withOpacity(0.3),
                                  ),
                                ))
                            .toList(),
                      ),
                    ],
                  ],
                ),
              ),
              if (store.isFavorite)
                Icon(
                  Icons.favorite,
                  color: Colors.red,
                  size: 24,
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAccountInfo() {
    return Observer(
      builder: (context) {
        final account = store.account!;

        return _buildSection(
          title: '账号信息',
          children: [
            _buildInfoItem(
              icon: Icons.person,
              label: '用户名',
              value: account.username,
              canCopy: true,
            ),
            if (account.email != null && account.email!.isNotEmpty)
              _buildInfoItem(
                icon: Icons.email,
                label: '邮箱',
                value: account.email!,
                canCopy: true,
              ),
            if (account.website != null && account.website!.isNotEmpty)
              _buildInfoItem(
                icon: Icons.language,
                label: '网站',
                value: account.website!,
                canCopy: true,
              ),
          ],
        );
      },
    );
  }

  Widget _buildPasswordSection() {
    return Observer(
      builder: (context) {
        final account = store.account!;

        return _buildSection(
          title: '密码',
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Row(
                children: [
                  const Icon(Icons.lock, color: Colors.grey),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      store.isPasswordVisible
                          ? (account.password ?? "")
                          : '••••••••',
                      style: const TextStyle(
                        fontSize: 16,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: store.togglePasswordVisibility,
                    icon: Icon(
                      store.isPasswordVisible
                          ? Icons.visibility_off
                          : Icons.visibility,
                    ),
                  ),
                  IconButton(
                    onPressed: () =>
                        store.copyToClipboard(account.password ?? "", '密码'),
                    icon: const Icon(Icons.copy),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildTwoFactorSection() {
    return Observer(
      builder: (context) {
        if (!store.hasTwoFactor) {
          return const SizedBox.shrink();
        }

        return _buildSection(
          title: '双因素认证 (2FA)',
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      const Icon(Icons.security, color: Colors.blue),
                      const SizedBox(width: 12),
                      const Expanded(
                        child: Text(
                          '验证码',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: store.refreshTwoFactorCode,
                        icon: const Icon(Icons.refresh, size: 20),
                        tooltip: '刷新验证码',
                      ),
                      IconButton(
                        onPressed: store.copyTwoFactorCode,
                        icon: const Icon(Icons.copy, size: 20),
                        tooltip: '复制验证码',
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: Text(
                            store.currentTwoFactorCode ?? '------',
                            style: const TextStyle(
                              fontSize: 24,
                              fontFamily: 'monospace',
                              fontWeight: FontWeight.bold,
                              letterSpacing: 4,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            CircularProgressIndicator(
                              value: store.twoFactorRemainingTime / 30.0,
                              strokeWidth: 3,
                              backgroundColor: Colors.grey.shade300,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                store.twoFactorRemainingTime <= 5
                                    ? Colors.red
                                    : Colors.blue,
                              ),
                            ),
                            Text(
                              '${store.twoFactorRemainingTime}',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: store.twoFactorRemainingTime <= 5
                                    ? Colors.red
                                    : Colors.blue,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildAdditionalInfo() {
    return Observer(
      builder: (context) {
        final account = store.account!;

        if (account.notes == null || account.notes!.isEmpty) {
          return const SizedBox.shrink();
        }

        return _buildSection(
          title: '备注',
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Text(
                account.notes!,
                style: const TextStyle(fontSize: 14),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildMetaInfo() {
    return Observer(
      builder: (context) {
        return _buildSection(
          title: '其他信息',
          children: [
            _buildInfoItem(
              icon: Icons.access_time,
              label: '最后使用',
              value: store.formattedLastUsed,
            ),
            _buildInfoItem(
              icon: Icons.calendar_today,
              label: '创建时间',
              value: store.formattedCreatedAt,
            ),
            _buildInfoItem(
              icon: Icons.update,
              label: '更新时间',
              value: store.formattedUpdatedAt,
            ),
          ],
        );
      },
    );
  }

  Widget _buildSection({
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
    bool canCopy = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey.shade600),
          const SizedBox(width: 12),
          Text(
            '$label:',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
          if (canCopy)
            IconButton(
              onPressed: () => store.copyToClipboard(value, label),
              icon: const Icon(Icons.copy, size: 16),
              constraints: const BoxConstraints(
                minWidth: 32,
                minHeight: 32,
              ),
            ),
        ],
      ),
    );
  }

  IconData _getIconData(String iconName) {
    // 这里应该与IconSelector中的图标映射保持一致
    switch (iconName) {
      case 'work':
        return Icons.work;
      case 'email':
        return Icons.email;
      case 'shopping_cart':
        return Icons.shopping_cart;
      case 'games':
        return Icons.games;
      case 'music_note':
        return Icons.music_note;
      case 'movie':
        return Icons.movie;
      case 'sports':
        return Icons.sports;
      case 'school':
        return Icons.school;
      case 'local_hospital':
        return Icons.local_hospital;
      case 'restaurant':
        return Icons.restaurant;
      case 'directions_car':
        return Icons.directions_car;
      case 'home':
        return Icons.home;
      case 'account_balance':
        return Icons.account_balance;
      case 'credit_card':
        return Icons.credit_card;
      case 'security':
        return Icons.security;
      case 'cloud':
        return Icons.cloud;
      case 'phone':
        return Icons.phone;
      case 'computer':
        return Icons.computer;
      case 'wifi':
        return Icons.wifi;
      case 'vpn_key':
        return Icons.vpn_key;
      case 'lock':
        return Icons.lock;
      case 'folder':
        return Icons.folder;
      case 'star':
        return Icons.star;
      case 'favorite':
        return Icons.favorite;
      case 'bookmark':
        return Icons.bookmark;
      case 'label':
        return Icons.label;
      case 'more_horiz':
        return Icons.more_horiz;
      default:
        return Icons.category;
    }
  }

  Future<void> _handleMenuAction(String action) async {
    switch (action) {
      case 'edit':
        context.push('/edit-account/${widget.accountId}');
        break;
      case 'favorite':
        await store.toggleFavorite();
        break;
      case 'delete':
        _showDeleteDialog();
        break;
    }
  }

  Future<void> _showDeleteDialog() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除账号'),
        content: const Text('确定要删除这个账号吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await store.deleteAccount();
      if (success && mounted) {
        context.pop();
      }
    }
  }
}
