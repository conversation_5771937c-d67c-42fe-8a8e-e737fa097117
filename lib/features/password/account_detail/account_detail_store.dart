import 'dart:async';
import 'package:flutter/services.dart';
import 'package:mobx/mobx.dart';
import 'package:injectable/injectable.dart';
import 'package:password/features/category/category_manage/category_manage_store.dart';
import 'package:password/features/password/stores/account_store.dart';
import '../../../model/account_model.dart';
import '../../../../shared/models/category_model.dart';
import '../../../../shared/utils/toast_utils.dart';
import '../../../../services/two_factor_service.dart';

part 'account_detail_store.g.dart';

/// 账号详情页面状态管理
@injectable
class AccountDetailStore = _AccountDetailStore with _$AccountDetailStore;

abstract class _AccountDetailStore with Store {
  final AccountStore _accountStore;
  final CategoryManageStore _categoryStore;

  _AccountDetailStore(this._accountStore, this._categoryStore);

  // Observable状态
  @observable
  bool isLoading = false;

  @observable
  String? errorMessage;

  @observable
  AccountModel? account;

  @observable
  CategoryModel? category;

  @observable
  bool isPasswordVisible = false;

  @observable
  bool isEditMode = false;

  @observable
  String? currentTwoFactorCode;

  @observable
  int twoFactorRemainingTime = 30;

  Timer? _twoFactorTimer;

  // Computed values
  @computed
  bool get hasAccount => account != null;

  @computed
  String get categoryName => category?.name ?? '未知分类';

  @computed
  String get categoryIcon => category?.icon ?? 'category';

  @computed
  String get categoryColor => category?.color ?? '#4286EA';

  @computed
  bool get isFavorite => account?.isFavorite ?? false;

  @computed
  bool get hasTwoFactor => false;

  @computed
  String get formattedCreatedAt {
    if (account?.createdAt == null) return '';
    final date = account!.createdAt;
    if (date == null) return "";
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} '
        '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  @computed
  String get formattedUpdatedAt {
    if (account?.updatedAt == null) return '';
    final date = account!.updatedAt;
    if (date == null) return "";
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} '
        '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  @computed
  String get formattedLastUsed {
    if (account?.lastUsed == null) return '从未使用';
    final date = account!.lastUsed!;
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  // Actions
  @action
  Future<void> initialize(String accountId) async {
    isLoading = true;
    errorMessage = null;

    try {
      await _accountStore.initialize();
      await _categoryStore.initialize();

      account = _accountStore.getAccountById(accountId);
      if (account == null) {
        throw Exception('账号不存在');
      }

      category = account!.categoryId != null
          ? _categoryStore.getCategoryById(account!.categoryId!)
          : null;

      // 更新最后使用时间
      await _accountStore.updateLastUsed(accountId);

      // 如果启用了2FA，开始生成验证码
      if (hasTwoFactor) {
        _startTwoFactorTimer();
      }
    } catch (e) {
      errorMessage = '加载账号详情失败：$e';
    } finally {
      isLoading = false;
    }
  }

  @action
  void togglePasswordVisibility() {
    isPasswordVisible = !isPasswordVisible;
  }

  @action
  void toggleEditMode() {
    isEditMode = !isEditMode;
  }

  @action
  Future<bool> toggleFavorite() async {
    if (account == null) return false;

    try {
      final success = await _accountStore.toggleFavorite(account!.id);
      if (success) {
        // 重新获取账号信息以更新状态
        account = _accountStore.getAccountById(account!.id);
      }
      return success;
    } catch (e) {
      ToastUtils.showError('更新收藏状态失败');
      return false;
    }
  }

  @action
  Future<bool> deleteAccount() async {
    if (account == null) return false;

    try {
      return await _accountStore.deleteAccount(account!.id);
    } catch (e) {
      ToastUtils.showError('删除账号失败');
      return false;
    }
  }

  @action
  Future<void> copyToClipboard(String text, String label) async {
    try {
      await Clipboard.setData(ClipboardData(text: text));
      ToastUtils.showSuccess('$label已复制到剪贴板');
    } catch (e) {
      ToastUtils.showError('复制失败');
    }
  }

  @action
  void clearError() {
    errorMessage = null;
  }

  @action
  void _startTwoFactorTimer() {
    _updateTwoFactorCode();
    _twoFactorTimer?.cancel();
    _twoFactorTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateTwoFactorCode();
    });
  }

  @action
  void _updateTwoFactorCode() {
    if (!hasTwoFactor) return;

    try {
      twoFactorRemainingTime = TwoFactorService.getRemainingTime();
    } catch (e) {
      currentTwoFactorCode = null;
      twoFactorRemainingTime = 0;
    }
  }

  @action
  void refreshTwoFactorCode() {
    if (hasTwoFactor) {
      _updateTwoFactorCode();
    }
  }

  @action
  Future<void> copyTwoFactorCode() async {
    if (currentTwoFactorCode != null) {
      await Clipboard.setData(ClipboardData(text: currentTwoFactorCode!));
      ToastUtils.showSuccess('验证码已复制');
    }
  }

  void dispose() {
    _twoFactorTimer?.cancel();
  }
}
