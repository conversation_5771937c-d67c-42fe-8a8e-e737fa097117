import 'dart:convert';
import 'package:mobx/mobx.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../shared/models/user_model.dart';

part 'user_store.g.dart';

/// 用户状态管理
@singleton
class UserStore = _UserStore with _$UserStore;

abstract class _UserStore with Store {
  static const String _userKey = 'current_user';

  // Observable状态
  @observable
  UserModel? currentUser;

  @observable
  bool isLoading = false;

  @observable
  String? errorMessage;

  /// 初始化用户数据
  @action
  Future<void> initialize() async {
    isLoading = true;
    try {
      await _loadUser();
    } catch (e) {
      errorMessage = '加载用户信息失败: $e';
    } finally {
      isLoading = false;
    }
  }

  /// 加载用户信息
  Future<void> _loadUser() async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = prefs.getString(_userKey);
    
    if (userJson != null) {
      try {
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        currentUser = UserModel.fromJson(userMap);
      } catch (e) {
        // 如果解析失败，创建默认用户
        await _createDefaultUser();
      }
    } else {
      // 如果没有用户信息，创建默认用户
      await _createDefaultUser();
    }
  }

  /// 创建默认用户
  Future<void> _createDefaultUser() async {
    final now = DateTime.now();
    final defaultUser = UserModel(
      id: 'default_user_${now.millisecondsSinceEpoch}',
      name: '用户',
      createdAt: now,
      updatedAt: now,
    );
    
    await updateUser(defaultUser);
  }

  /// 保存用户信息
  Future<void> _saveUser(UserModel user) async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = jsonEncode(user.toJson());
    await prefs.setString(_userKey, userJson);
  }

  /// 更新用户信息
  @action
  Future<bool> updateUser(UserModel user) async {
    isLoading = true;
    errorMessage = null;

    try {
      final updatedUser = user.copyWith(updatedAt: DateTime.now());
      await _saveUser(updatedUser);
      currentUser = updatedUser;
      return true;
    } catch (e) {
      errorMessage = '更新用户信息失败: $e';
      return false;
    } finally {
      isLoading = false;
    }
  }

  /// 更新用户名
  @action
  Future<bool> updateUserName(String name) async {
    if (currentUser == null) return false;
    
    final updatedUser = currentUser!.copyWith(name: name);
    return await updateUser(updatedUser);
  }

  /// 更新用户邮箱
  @action
  Future<bool> updateUserEmail(String? email) async {
    if (currentUser == null) return false;
    
    final updatedUser = currentUser!.copyWith(email: email);
    return await updateUser(updatedUser);
  }

  /// 更新用户头像
  @action
  Future<bool> updateUserAvatar(String? avatar) async {
    if (currentUser == null) return false;
    
    final updatedUser = currentUser!.copyWith(avatar: avatar);
    return await updateUser(updatedUser);
  }

  /// 设置VIP状态
  @action
  Future<bool> setVipStatus(bool isVip, DateTime? expireDate) async {
    if (currentUser == null) return false;
    
    final updatedUser = currentUser!.copyWith(
      isVip: isVip,
      vipExpireDate: expireDate,
    );
    return await updateUser(updatedUser);
  }

  /// 清除错误信息
  @action
  void clearError() {
    errorMessage = null;
  }

  // Computed values
  @computed
  bool get isLoggedIn => currentUser != null;

  @computed
  bool get isVip => currentUser?.isVipValid ?? false;

  @computed
  String get userName => currentUser?.displayName ?? '用户';

  @computed
  String get userAvatarText => currentUser?.avatarText ?? '用';

  @computed
  String? get userAvatar => currentUser?.avatar;

  @computed
  int? get vipRemainingDays => currentUser?.vipRemainingDays;
}
