import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:mobx/mobx.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../shared/models/category_model.dart';
import '../../../../shared/utils/toast_utils.dart';

part 'category_manage_store.g.dart';

/// 分类管理状态管理
@singleton
class CategoryManageStore = _CategoryManageStore with _$CategoryManageStore;

abstract class _CategoryManageStore with Store {
  static const String _categoriesKey = 'categories';
  static const String _defaultCategoriesLoadedKey = 'default_categories_loaded';

  // Observable状态
  @observable
  ObservableList<CategoryModel> categories = ObservableList<CategoryModel>();

  @observable
  bool isLoading = false;

  @observable
  String? errorMessage;

  /// 初始化分类数据
  @action
  Future<void> initialize() async {
    isLoading = true;
    try {
      await _loadCategories();
    } catch (e) {
      errorMessage = '加载分类失败: $e';
    } finally {
      isLoading = false;
    }
  }

  /// 加载分类数据
  @action
  Future<void> _loadCategories() async {
    final prefs = await SharedPreferences.getInstance();

    // 检查是否已加载默认分类
    final defaultLoaded = prefs.getBool(_defaultCategoriesLoadedKey) ?? false;

    if (!defaultLoaded) {
      await _loadDefaultCategories();
      await prefs.setBool(_defaultCategoriesLoadedKey, true);
    }

    // 从本地存储加载分类
    final categoriesJson = prefs.getStringList(_categoriesKey) ?? [];
    final loadedCategories = categoriesJson
        .map((json) => CategoryModel.fromJson(jsonDecode(json)))
        .toList();

    // 按order排序
    loadedCategories.sort((a, b) => a.order.compareTo(b.order));
    categories.clear();
    categories.addAll(loadedCategories);
  }

  /// 加载默认分类数据
  Future<void> _loadDefaultCategories() async {
    try {
      final jsonString =
          await rootBundle.loadString('assets/cache/default.json');
      final data = jsonDecode(jsonString) as Map<String, dynamic>;
      final defaultCategories = (data['categories'] as List)
          .map((json) => CategoryModel.fromJson(json))
          .toList();

      await _saveCategories(defaultCategories);
    } catch (e) {
      print('加载默认分类失败: $e');
      // 如果加载失败，创建一个基本的"其他"分类
      final otherCategory = CategoryModel(
        id: 'other',
        name: '其他',
        icon: 'more_horiz',
        color: '#636E72',
        description: '其他类型的账号',
        isDefault: true,
        order: 1,
        createdAt: DateTime.now(),
      );
      await _saveCategories([otherCategory]);
    }
  }

  /// 保存分类到本地存储
  @action
  Future<void> _saveCategories(List<CategoryModel> categoriesToSave) async {
    final prefs = await SharedPreferences.getInstance();
    final categoriesJson = categoriesToSave
        .map((category) => jsonEncode(category.toJson()))
        .toList();
    await prefs.setStringList(_categoriesKey, categoriesJson);
    categories.clear();
    categories.addAll(categoriesToSave);
  }

  /// 添加分类
  @action
  Future<bool> addCategory(CategoryModel category) async {
    try {
      final updatedCategories = List<CategoryModel>.from(categories);
      updatedCategories.add(category);
      await _saveCategories(updatedCategories);
      ToastUtils.showSuccess('分类添加成功');
      return true;
    } catch (e) {
      errorMessage = '添加分类失败: $e';
      ToastUtils.showError('添加分类失败');
      return false;
    }
  }

  /// 更新分类
  @action
  Future<bool> updateCategory(CategoryModel category) async {
    try {
      final updatedCategories =
          categories.map((c) => c.id == category.id ? category : c).toList();
      await _saveCategories(updatedCategories);
      ToastUtils.showSuccess('分类更新成功');
      return true;
    } catch (e) {
      errorMessage = '更新分类失败: $e';
      ToastUtils.showError('更新分类失败');
      return false;
    }
  }

  /// 删除分类
  @action
  Future<bool> deleteCategory(String categoryId) async {
    try {
      final updatedCategories =
          categories.where((c) => c.id != categoryId).toList();
      await _saveCategories(updatedCategories);
      ToastUtils.showSuccess('分类删除成功');
      return true;
    } catch (e) {
      errorMessage = '删除分类失败: $e';
      ToastUtils.showError('删除分类失败');
      return false;
    }
  }

  /// 根据ID获取分类
  CategoryModel? getCategoryById(String id) {
    try {
      return categories.firstWhere((c) => c.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 获取自定义分类（非默认分类）
  List<CategoryModel> getCustomCategories() {
    return categories.where((c) => !c.isDefault).toList();
  }

  /// 重新排序分类
  @action
  Future<bool> reorderCategories(
      List<CategoryModel> reorderedCategories) async {
    try {
      // 更新order字段
      final updatedCategories = reorderedCategories
          .asMap()
          .entries
          .map((entry) => entry.value.copyWith(order: entry.key + 1))
          .toList();

      await _saveCategories(updatedCategories);
      ToastUtils.showSuccess('分类排序已更新');
      return true;
    } catch (e) {
      errorMessage = '更新分类排序失败: $e';
      ToastUtils.showError('更新分类排序失败');
      return false;
    }
  }

  /// 清除错误信息
  @action
  void clearError() {
    errorMessage = null;
  }
}
