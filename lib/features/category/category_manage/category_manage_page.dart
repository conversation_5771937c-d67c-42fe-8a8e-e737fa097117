import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/di/injection.dart';
import '../../../../shared/models/category_model.dart';
import 'category_manage_store.dart';

/// 分类管理页面
class CategoryManagePage extends StatefulWidget {
  const CategoryManagePage({super.key});

  @override
  State<CategoryManagePage> createState() => _CategoryManagePageState();
}

class _CategoryManagePageState extends State<CategoryManagePage> {
  late final CategoryManageStore categoryStore;

  @override
  void initState() {
    super.initState();
    categoryStore = getIt<CategoryManageStore>();
    categoryStore.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('分类管理'),
        actions: [
          IconButton(
            onPressed: () => context.push('/edit-category'),
            icon: const Icon(Icons.add),
            tooltip: '添加分类',
          ),
        ],
      ),
      body: Observer(
        builder: (context) {
          if (categoryStore.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          final categories = categoryStore.categories.toList();

          if (categories.isEmpty) {
            return _buildEmptyState();
          }

          return ReorderableListView.builder(
            itemCount: categories.length,
            onReorder: (oldIndex, newIndex) {
              _onReorder(oldIndex, newIndex, categories);
            },
            itemBuilder: (context, index) {
              final category = categories[index];
              return _buildCategoryItem(category, key: ValueKey(category.id));
            },
          );
        },
      ),
    );
  }

  void _onReorder(int oldIndex, int newIndex, List<CategoryModel> categories) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }

    final reorderedCategories = List<CategoryModel>.from(categories);
    final item = reorderedCategories.removeAt(oldIndex);
    reorderedCategories.insert(newIndex, item);

    categoryStore.reorderCategories(reorderedCategories);
  }

  Widget _buildEmptyState() {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.category_outlined,
              size: 48,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              '还没有分类',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.grey.shade600,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              '点击右上角按钮添加分类',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey.shade500,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryItem(CategoryModel category, {required Key key}) {
    final index = categoryStore.categories.indexOf(category);
    return Container(
      key: key,
      color: index % 2 == 0
          ? Theme.of(context).colorScheme.surface
          : Theme.of(context).colorScheme.surfaceContainer,
      child: ListTile(
        leading: InkWell(
          onTap: () => _showDeleteCategoryDialog(category),
          child: Icon(
            Icons.remove_circle,
            color: Theme.of(context).colorScheme.error,
          ),
        ),
        title: Row(
          children: [
            const SizedBox(width: 8),
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: _getCategoryColor(category.color).withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                _getCategoryIcon(category.icon),
                color: _getCategoryColor(category.color),
                size: 18,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    category.name,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  if (category.description.isNotEmpty) ...[
                    const SizedBox(height: 2),
                    Text(
                      category.description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey.shade600,
                          ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
        trailing: const Icon(Icons.drag_indicator),
        onTap: () => context.push('/edit-category/${category.id}'),
      ),
    );
  }

  void _showDeleteCategoryDialog(CategoryModel category) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除分类'),
        content: Text('确定要删除分类"${category.name}"吗？\n\n注意：该分类下的账号将需要重新分类。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await categoryStore.deleteCategory(category.id);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  IconData _getCategoryIcon(String iconName) {
    switch (iconName) {
      case 'group':
        return Icons.group;
      case 'email':
        return Icons.email;
      case 'account_balance':
        return Icons.account_balance;
      case 'shopping_cart':
        return Icons.shopping_cart;
      case 'movie':
        return Icons.movie;
      case 'work':
        return Icons.work;
      case 'school':
        return Icons.school;
      case 'flight':
        return Icons.flight;
      case 'local_hospital':
        return Icons.local_hospital;
      case 'sports_esports':
        return Icons.sports_esports;
      case 'music_note':
        return Icons.music_note;
      case 'camera_alt':
        return Icons.camera_alt;
      case 'restaurant':
        return Icons.restaurant;
      case 'home':
        return Icons.home;
      case 'car_rental':
        return Icons.car_rental;
      case 'more_horiz':
        return Icons.more_horiz;
      default:
        return Icons.category;
    }
  }

  Color _getCategoryColor(String colorHex) {
    try {
      return Color(int.parse(colorHex.replaceFirst('#', '0xFF')));
    } catch (e) {
      return const Color(0xFF4286EA);
    }
  }
}
