import 'package:flutter/material.dart';
import 'package:mobx/mobx.dart';
import 'package:injectable/injectable.dart';
import '../../../../shared/models/category_model.dart';
import '../../../../shared/utils/toast_utils.dart';
import '../category_manage/category_manage_store.dart';

part 'category_edit_store.g.dart';

/// 编辑分类页面状态管理
@injectable
class CategoryEditStore = _CategoryEditStore with _$CategoryEditStore;

abstract class _CategoryEditStore with Store {
  final CategoryManageStore _categoryStore;

  _CategoryEditStore(this._categoryStore);

  // Controllers
  final TextEditingController nameController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();

  // Observable状态
  @observable
  bool isLoading = false;

  @observable
  bool isSaving = false;

  @observable
  String? errorMessage;

  @observable
  String? nameError;

  @observable
  String selectedIcon = 'category';

  @observable
  String selectedColor = '#4286EA';

  @observable
  CategoryModel? originalCategory;

  // Computed values
  @computed
  bool get canSave {
    return nameController.text.trim().isNotEmpty &&
        nameError == null &&
        !isSaving;
  }

  @computed
  bool get hasChanges {
    if (originalCategory == null) {
      // 新建分类，只要有名称就算有变化
      return nameController.text.trim().isNotEmpty;
    }

    // 编辑分类，检查是否有任何字段发生变化
    return nameController.text.trim() != originalCategory!.name ||
        descriptionController.text.trim() != originalCategory!.description ||
        selectedIcon != originalCategory!.icon ||
        selectedColor != originalCategory!.color;
  }

  // Actions
  @action
  Future<void> initialize(String? categoryId) async {
    isLoading = true;
    errorMessage = null;
    nameError = null;

    try {
      await _categoryStore.initialize();

      if (categoryId != null) {
        // 编辑模式
        originalCategory = _categoryStore.getCategoryById(categoryId);
        if (originalCategory == null) {
          throw Exception('分类不存在');
        }

        nameController.text = originalCategory!.name;
        descriptionController.text = originalCategory!.description;
        selectedIcon = originalCategory!.icon;
        selectedColor = originalCategory!.color;
      } else {
        // 新建模式
        originalCategory = null;
        nameController.clear();
        descriptionController.clear();
        selectedIcon = 'category';
        selectedColor = '#4286EA';
      }
    } catch (e) {
      errorMessage = '初始化失败：$e';
    } finally {
      isLoading = false;
    }
  }

  @action
  void setName(String value) {
    nameController.text = value;
    _validateName();
  }

  @action
  void setDescription(String value) {
    descriptionController.text = value;
  }

  @action
  void setIcon(String icon) {
    selectedIcon = icon;
  }

  @action
  void setColor(String color) {
    selectedColor = color;
  }

  @action
  void _validateName() {
    final name = nameController.text.trim();

    if (name.isEmpty) {
      nameError = '请输入分类名称';
      return;
    }

    if (name.length > 20) {
      nameError = '分类名称不能超过20个字符';
      return;
    }

    // 检查是否与其他分类重名（编辑时排除自己）
    final existingCategory = _categoryStore.categories
        .where((c) => c.name == name && c.id != originalCategory?.id)
        .firstOrNull;

    if (existingCategory != null) {
      nameError = '分类名称已存在';
      return;
    }

    nameError = null;
  }

  @action
  Future<bool> saveCategory() async {
    if (!canSave) return false;

    isSaving = true;
    try {
      final now = DateTime.now();
      final category = CategoryModel(
        id: originalCategory?.id ?? now.millisecondsSinceEpoch.toString(),
        name: nameController.text.trim(),
        icon: selectedIcon,
        color: selectedColor,
        description: descriptionController.text.trim(),
        isDefault: originalCategory?.isDefault ?? false,
        order: originalCategory?.order ?? _categoryStore.categories.length + 1,
        createdAt: originalCategory?.createdAt ?? now,
        updatedAt: now,
      );

      bool success;
      if (originalCategory != null) {
        success = await _categoryStore.updateCategory(category);
      } else {
        success = await _categoryStore.addCategory(category);
      }

      return success;
    } catch (e) {
      ToastUtils.showError('保存失败：$e');
      return false;
    } finally {
      isSaving = false;
    }
  }

  @action
  Future<bool> deleteCategory() async {
    if (originalCategory == null) {
      ToastUtils.showWarning('分类不存在');
      return false;
    }

    try {
      return await _categoryStore.deleteCategory(originalCategory!.id);
    } catch (e) {
      ToastUtils.showError('删除失败：$e');
      return false;
    }
  }

  @action
  void clearError() {
    errorMessage = null;
    nameError = null;
  }

  void dispose() {
    nameController.dispose();
    descriptionController.dispose();
  }
}
