import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:password/features/password/widgets/account_list_item.dart';
import '../../../core/di/injection.dart';
import '../../../core/router/router_path.dart';
import '../../../model/account_model.dart';
import 'search_store.dart';
import 'widgets/search_history_item.dart';
import 'widgets/empty_search_state.dart';

/// 搜索页面
class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  late final SearchStore store;

  @override
  void initState() {
    super.initState();
    store = getIt<SearchStore>();
    _initializeStore();
  }

  Future<void> _initializeStore() async {
    await store.initialize();
  }

  @override
  void dispose() {
    store.dispose();
    super.dispose();
  }

  void _onAccountTap(AccountModel account) {
    context.push(RouterPath.accountDetail.replaceAll(':id', account.id));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: _buildAppBar(),
      body: Observer(
        builder: (context) {
          if (store.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (store.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '错误: ${store.errorMessage}',
                    style: const TextStyle(color: Colors.red),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      store.clearError();
                      store.initialize();
                    },
                    child: const Text('重试'),
                  ),
                ],
              ),
            );
          }

          return _buildContent();
        },
      ),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      title: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Row(
          children: [
            // 返回按钮
            IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.black),
              onPressed: () => context.pop(),
            ),

            // 搜索输入框
            Expanded(
              child: Container(
                height: 40,
                margin: const EdgeInsets.symmetric(horizontal: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: TextField(
                  controller: store.searchController,
                  focusNode: store.focusNode,
                  autofocus: true,
                  decoration: InputDecoration(
                    hintText: '搜索账号、用户名、邮箱...',
                    hintStyle: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 14,
                    ),
                    prefixIcon: Icon(
                      Icons.search,
                      color: Colors.grey[500],
                      size: 20,
                    ),
                    suffixIcon: Observer(
                      builder: (context) {
                        if (store.searchQuery.isNotEmpty) {
                          return IconButton(
                            icon: Icon(
                              Icons.clear,
                              color: Colors.grey[500],
                              size: 18,
                            ),
                            onPressed: store.clearSearch,
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 10,
                    ),
                  ),
                  style: const TextStyle(fontSize: 14),
                  onChanged: store.onSearchInputChanged,
                ),
              ),
            ),

            // 确认搜索按钮
            Observer(
              builder: (context) {
                if (store.searchQuery.isNotEmpty) {
                  return Container(
                    margin: const EdgeInsets.only(right: 8),
                    child: TextButton(
                      onPressed:
                          store.isLoading ? null : () => store.confirmSearch(),
                      style: TextButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        minimumSize: const Size(60, 40),
                      ),
                      child: store.isLoading
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Text(
                              '搜索',
                              style: TextStyle(fontSize: 14),
                            ),
                    ),
                  );
                }
                return const SizedBox(width: 8);
              },
            ),
          ],
        ),
      ),
      actions: [
        // 清空历史按钮（移到菜单中）
        Observer(
          builder: (context) {
            if (store.hasSearchHistory && store.searchQuery.isEmpty) {
              return PopupMenuButton<String>(
                icon: Icon(Icons.more_vert, color: Colors.grey[600]),
                onSelected: (value) {
                  if (value == 'clear_history') {
                    _showClearHistoryDialog();
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'clear_history',
                    child: Row(
                      children: [
                        Icon(Icons.clear_all, size: 20),
                        SizedBox(width: 8),
                        Text('清空搜索历史'),
                      ],
                    ),
                  ),
                ],
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ],
    );
  }

  /// 构建内容区域
  Widget _buildContent() {
    return Observer(
      builder: (context) {
        // 显示搜索历史
        if (store.showSearchHistory) {
          return _buildSearchHistory();
        }

        // 显示搜索结果
        if (store.showSearchResults) {
          if (store.showEmptyState) {
            return EmptySearchState(
              searchQuery: store.searchQuery,
              onRetry: () => store.search(store.searchQuery),
            );
          }
          return _buildSearchResults();
        }

        // 默认状态
        return const EmptyHistoryState();
      },
    );
  }

  /// 构建搜索历史
  Widget _buildSearchHistory() {
    return Observer(
      builder: (context) {
        if (!store.hasSearchHistory) {
          return const EmptyHistoryState();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 历史标题
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Text(
                '搜索历史',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),

            // 历史列表
            Expanded(
              child: ListView.builder(
                itemCount: store.searchHistory.length,
                itemBuilder: (context, index) {
                  final query = store.searchHistory[index];
                  return SearchHistoryItem(
                    query: query,
                    onTap: () => store.onHistoryItemTap(query),
                    onDelete: () => store.removeHistoryItem(query),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  /// 构建搜索结果
  Widget _buildSearchResults() {
    return Observer(
      builder: (context) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 结果统计
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Text(
                '找到 ${store.searchResults.length} 个结果',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ),

            // 结果列表
            Expanded(
              child: ListView.builder(
                itemCount: store.searchResults.length,
                itemBuilder: (context, index) {
                  final account = store.searchResults[index];
                  return AccountListItem(
                    account: account,
                    category: store.getCategoryById(account.categoryId),
                    onTap: () => _onAccountTap(account),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  /// 显示清空历史对话框
  void _showClearHistoryDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清空搜索历史'),
        content: const Text('确定要清空所有搜索历史吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              store.clearSearchHistory();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
