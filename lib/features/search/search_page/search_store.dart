import 'package:flutter/material.dart';
import 'package:mobx/mobx.dart';
import 'package:injectable/injectable.dart';
import '../../../core/services/search_history_service.dart';
import '../../../model/account_model.dart';
import '../../../shared/models/category_model.dart';
import '../../password/stores/account_store.dart';
import '../../category/category_manage/category_manage_store.dart';

part 'search_store.g.dart';

/// 搜索页面状态管理
@injectable
class SearchStore = _SearchStore with _$SearchStore;

abstract class _SearchStore with Store {
  final SearchHistoryService _searchHistoryService;
  final AccountStore _accountStore;
  final CategoryManageStore _categoryStore;

  _SearchStore(
    this._searchHistoryService,
    this._accountStore,
    this._categoryStore,
  );

  // 搜索控制器
  final searchController = TextEditingController();
  final focusNode = FocusNode();

  // Observable状态
  @observable
  String searchQuery = '';

  @observable
  bool isSearching = false;

  @observable
  bool hasFocus = false;

  @observable
  List<String> searchHistory = [];

  @observable
  List<AccountModel> searchResults = [];

  @observable
  bool isLoading = false;

  @observable
  String? errorMessage;

  // Computed属性
  @computed
  bool get hasSearchResults => searchResults.isNotEmpty;

  @computed
  bool get hasSearchHistory => searchHistory.isNotEmpty;

  @computed
  bool get showSearchHistory => searchQuery.isEmpty;

  @computed
  bool get showSearchResults => isSearching && searchResults.isNotEmpty;

  @computed
  bool get showEmptyState => isSearching && searchResults.isEmpty;

  /// 初始化
  @action
  Future<void> initialize() async {
    try {
      await loadSearchHistory();
      _setupFocusListener();
    } catch (e) {
      errorMessage = '初始化失败: $e';
    }
  }

  /// 设置焦点监听
  void _setupFocusListener() {
    focusNode.addListener(() {
      hasFocus = focusNode.hasFocus;
    });
  }

  /// 加载搜索历史
  @action
  Future<void> loadSearchHistory() async {
    try {
      searchHistory = await _searchHistoryService.getSearchHistory();
    } catch (e) {
      print('加载搜索历史失败: $e');
      searchHistory = [];
    }
  }

  /// 执行搜索
  @action
  Future<void> search(String query) async {
    if (query.trim().isEmpty) {
      searchQuery = '';
      searchResults = [];
      return;
    }

    try {
      isLoading = true;
      errorMessage = null;
      searchQuery = query.trim();

      // 执行搜索
      searchResults = _accountStore.searchAccounts(searchQuery);

      // 添加到搜索历史
      await _searchHistoryService.addSearchHistory(searchQuery);
      await loadSearchHistory();

      isSearching = true;
    } catch (e) {
      errorMessage = '搜索失败: $e';
      searchResults = [];
    } finally {
      isLoading = false;
    }
  }

  /// 搜索输入变化（仅更新输入状态，不执行搜索）
  @action
  void onSearchInputChanged(String value) {
    searchQuery = value.trim();
    if (searchQuery.isEmpty) {
      searchResults = [];
      isSearching = false;
    }
  }

  /// 确认搜索（点击搜索按钮时执行）
  @action
  Future<void> confirmSearch() async {
    if (searchQuery.trim().isEmpty) return;

    await search(searchQuery);
  }

  /// 点击搜索历史项
  @action
  Future<void> onHistoryItemTap(String historyQuery) async {
    searchController.text = historyQuery;
    await search(historyQuery);
    focusNode.unfocus();
  }

  /// 删除搜索历史项
  @action
  Future<void> removeHistoryItem(String query) async {
    try {
      await _searchHistoryService.removeSearchHistory(query);
      await loadSearchHistory();
    } catch (e) {
      errorMessage = '删除搜索历史失败: $e';
    }
  }

  /// 清空搜索历史
  @action
  Future<void> clearSearchHistory() async {
    try {
      await _searchHistoryService.clearSearchHistory();
      await loadSearchHistory();
    } catch (e) {
      errorMessage = '清空搜索历史失败: $e';
    }
  }

  /// 清空搜索
  @action
  void clearSearch() {
    searchController.clear();
    searchQuery = '';
    searchResults = [];
    isSearching = false;
    focusNode.unfocus();
  }

  /// 获取分类信息
  CategoryModel? getCategoryById(String? categoryId) {
    if (categoryId == null) return null;
    return _categoryStore.getCategoryById(categoryId);
  }

  /// 清除错误信息
  @action
  void clearError() {
    errorMessage = null;
  }

  /// 释放资源
  void dispose() {
    searchController.dispose();
    focusNode.dispose();
  }
}
