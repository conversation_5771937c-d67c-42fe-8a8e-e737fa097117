import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:password/features/backup/qr_scanner/qr_scanner_page.dart';
import 'package:qr_code_tools/qr_code_tools.dart';

/// 二维码导入服务
class QrImportService {
  static final ImagePicker _picker = ImagePicker();

  /// 从图片文件导入2FA密钥
  /// 返回解析出的2FA信息，如果失败返回null
  static Future<String?> importFromImage() async {
    try {
      // 选择图片
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (image == null) {
        return null;
      }

      // 使用qr_code_tools解析二维码
      final String? qrData = await _parseQrFromImageFile(image.path);

      return qrData;
    } catch (e) {
      print('导入二维码失败: $e');
      return null;
    }
  }

  /// 从相机中获取二维码的数据
  static Future<String?> importFromCamera(BuildContext context) async {
    try {
      // 检查相机权限
      final hasPermission = await _checkCameraPermission();
      if (!hasPermission) {
        throw Exception('需要相机权限才能扫描二维码');
      }

      final String? qrData = await Navigator.of(context).push<String>(
        MaterialPageRoute(
          builder: (context) => const QrScannerPage(),
        ),
      );
      return qrData;
    } catch (e) {
      print('扫描二维码失败: $e');
      return null;
    }
  }

  /// 检查相机权限
  static Future<bool> _checkCameraPermission() async {
    try {
      // 这里可以添加权限检查逻辑
      // 目前先返回true，依赖系统权限对话框
      return true;
    } catch (e) {
      print('检查相机权限失败: $e');
      return false;
    }
  }

  /// 从图片文件解析二维码内容
  static Future<String?> _parseQrFromImageFile(String filePath) async {
    try {
      // 使用qr_code_tools解析图片中的二维码
      final String? qrData = await QrCodeToolsPlugin.decodeFrom(filePath);

      return qrData;
    } catch (e) {
      print('解析图片二维码失败: $e');
      return null;
    }
  }
}
