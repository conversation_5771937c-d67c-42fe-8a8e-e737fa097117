import 'package:flutter/material.dart';
import '../../../../shared/models/webdav_config.dart';
import '../../../../services/backup_service.dart';
import '../../../../shared/utils/toast_utils.dart';
import '../../../../core/di/injection.dart';

/// WebDAV配置页面
class WebdavConfigPage extends StatefulWidget {
  const WebdavConfigPage({super.key});

  @override
  State<WebdavConfigPage> createState() => _WebdavConfigPageState();
}

class _WebdavConfigPageState extends State<WebdavConfigPage> {
  final _formKey = GlobalKey<FormState>();
  final _serverUrlController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _directoryController = TextEditingController(text: 'password_backup');

  late final BackupService _backupService;

  bool _isLoading = false;
  bool _isPasswordVisible = false;
  bool _isTesting = false;

  @override
  void initState() {
    super.initState();
    _backupService = getIt<BackupService>();
    _loadConfig();
  }

  @override
  void dispose() {
    _serverUrlController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _directoryController.dispose();
    super.dispose();
  }

  /// 加载现有配置
  Future<void> _loadConfig() async {
    setState(() => _isLoading = true);

    try {
      final config = await _backupService.getWebdavConfig();
      if (config != null) {
        _serverUrlController.text = config.serverUrl;
        _usernameController.text = config.username;
        _passwordController.text = config.password;
        _directoryController.text = config.directory;
      }
    } catch (e) {
      ToastUtils.showError('加载配置失败: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 测试连接
  Future<void> _testConnection() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isTesting = true);

    try {
      final config = WebdavConfig(
        serverUrl: _serverUrlController.text.trim(),
        username: _usernameController.text.trim(),
        password: _passwordController.text,
        directory: _directoryController.text.trim(),
      );

      final success = await _backupService.testWebdavConnection(config);

      if (success) {
        ToastUtils.showSuccess('连接测试成功！');
      } else {
        ToastUtils.showError('连接测试失败，请检查配置');
      }
    } catch (e) {
      ToastUtils.showError('连接测试失败: $e');
    } finally {
      setState(() => _isTesting = false);
    }
  }

  /// 保存配置
  Future<void> _saveConfig() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final config = WebdavConfig(
        serverUrl: _serverUrlController.text.trim(),
        username: _usernameController.text.trim(),
        password: _passwordController.text,
        directory: _directoryController.text.trim(),
      );

      final success = await _backupService.saveWebdavConfig(config);

      if (success) {
        ToastUtils.showSuccess('配置保存成功！');
        Navigator.of(context).pop(true);
      } else {
        ToastUtils.showError('配置保存失败');
      }
    } catch (e) {
      ToastUtils.showError('保存配置失败: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('WebDAV配置'),
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildInfoCard(),
                    const SizedBox(height: 24),
                    _buildConfigForm(),
                    const SizedBox(height: 32),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
    );
  }

  /// 构建信息卡片
  Widget _buildInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.cloud, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'WebDAV 同步',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              '配置 WebDAV 服务可以将您的密码数据备份到自己的云存储中，支持 NextCloud、Seafile、坚果云等服务。',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建配置表单
  Widget _buildConfigForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '服务器配置',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 16),

        // 服务器地址
        TextFormField(
          controller: _serverUrlController,
          decoration: const InputDecoration(
            labelText: '服务器地址',
            hintText: 'https://your-server.com/webdav',
            prefixIcon: Icon(Icons.link),
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入服务器地址';
            }
            if (!value.startsWith('http://') && !value.startsWith('https://')) {
              return '服务器地址必须以 http:// 或 https:// 开头';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // 用户名
        TextFormField(
          controller: _usernameController,
          decoration: const InputDecoration(
            labelText: '用户名',
            prefixIcon: Icon(Icons.person),
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入用户名';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // 密码
        TextFormField(
          controller: _passwordController,
          obscureText: !_isPasswordVisible,
          decoration: InputDecoration(
            labelText: '密码',
            prefixIcon: const Icon(Icons.lock),
            suffixIcon: IconButton(
              icon: Icon(
                  _isPasswordVisible ? Icons.visibility_off : Icons.visibility),
              onPressed: () {
                setState(() => _isPasswordVisible = !_isPasswordVisible);
              },
            ),
            border: const OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '请输入密码';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // 备份目录
        TextFormField(
          controller: _directoryController,
          decoration: const InputDecoration(
            labelText: '备份目录',
            hintText: 'password_backup',
            prefixIcon: Icon(Icons.folder),
            border: OutlineInputBorder(),
            helperText: '备份文件将保存在此目录下',
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入备份目录';
            }
            return null;
          },
        ),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _isTesting ? null : _testConnection,
            icon: _isTesting
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.wifi_protected_setup),
            label: Text(_isTesting ? '测试中...' : '测试连接'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: FilledButton.icon(
            onPressed: _isLoading ? null : _saveConfig,
            icon: const Icon(Icons.save),
            label: const Text('保存配置'),
            style: FilledButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }
}
