import 'package:flutter/material.dart';
import '../../../model/netmodel/backup_net_model.dart';
import '../../../../services/backup_service.dart';
import '../../../../shared/utils/toast_utils.dart';
import '../../../../core/di/injection.dart';
import '../webdav_config/webdav_config_page.dart';

/// 备份管理页面
class BackupManagePage extends StatefulWidget {
  const BackupManagePage({super.key});

  @override
  State<BackupManagePage> createState() => _BackupManagePageState();
}

class _BackupManagePageState extends State<BackupManagePage> {
  late final BackupService _backupService;

  List<BackupHistory> _backupHistory = [];
  List<String> _webdavBackups = [];
  bool _isLoading = false;
  bool _isBackingUp = false;
  String _backupProgress = '';

  @override
  void initState() {
    super.initState();
    _backupService = getIt<BackupService>();
    _loadData();
  }

  /// 加载数据
  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final history = await _backupService.getBackupHistory();
      final webdavBackups = await _backupService.getWebdavBackupList();

      setState(() {
        _backupHistory = history;
        _webdavBackups = webdavBackups;
      });
    } catch (e) {
      ToastUtils.showError('加载数据失败: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 执行WebDAV备份
  Future<void> _performWebdavBackup() async {
    setState(() {
      _isBackingUp = true;
      _backupProgress = '正在准备备份...';
    });

    try {
      await _backupService.backupToWebdav(
        onProgress: (message) {
          setState(() => _backupProgress = message);
        },
      );

      ToastUtils.showSuccess('备份完成！');
      await _loadData(); // 刷新数据
    } catch (e) {
      ToastUtils.showError('备份失败: $e');
    } finally {
      setState(() {
        _isBackingUp = false;
        _backupProgress = '';
      });
    }
  }

  /// 从WebDAV恢复
  Future<void> _restoreFromWebdav(String fileName) async {
    final confirmed = await _showConfirmDialog(
      '确认恢复',
      '恢复备份将覆盖当前所有数据，此操作不可撤销。确定要继续吗？',
    );

    if (!confirmed) return;

    setState(() => _isLoading = true);

    try {
      await _backupService.restoreFromWebdav(
        fileName,
        onProgress: (message) {
          // 可以显示进度对话框
        },
      );

      ToastUtils.showSuccess('恢复完成！');
      await _loadData(); // 刷新数据
    } catch (e) {
      ToastUtils.showError('恢复失败: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 删除WebDAV备份
  Future<void> _deleteWebdavBackup(String fileName) async {
    final confirmed = await _showConfirmDialog(
      '确认删除',
      '确定要删除备份文件 "$fileName" 吗？此操作不可撤销。',
    );

    if (!confirmed) return;

    try {
      final success = await _backupService.deleteWebdavBackup(fileName);
      if (success) {
        ToastUtils.showSuccess('删除成功');
        await _loadData(); // 刷新数据
      } else {
        ToastUtils.showError('删除失败');
      }
    } catch (e) {
      ToastUtils.showError('删除失败: $e');
    }
  }

  /// 显示确认对话框
  Future<bool> _showConfirmDialog(String title, String content) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('确定'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  /// 打开WebDAV配置页面
  Future<void> _openWebdavConfig() async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(builder: (context) => const WebdavConfigPage()),
    );

    if (result == true) {
      await _loadData(); // 配置更新后刷新数据
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('数据备份'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _openWebdavConfig,
            tooltip: 'WebDAV配置',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildBackupSection(),
                    const SizedBox(height: 24),
                    _buildWebdavBackupsSection(),
                    const SizedBox(height: 24),
                    _buildHistorySection(),
                  ],
                ),
              ),
            ),
    );
  }

  /// 构建备份操作区域
  Widget _buildBackupSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.backup, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  '创建备份',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              '将您的密码数据备份到WebDAV服务器，确保数据安全。',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            if (_isBackingUp) ...[
              LinearProgressIndicator(),
              const SizedBox(height: 8),
              Text(_backupProgress,
                  style: Theme.of(context).textTheme.bodySmall),
              const SizedBox(height: 16),
            ],
            SizedBox(
              width: double.infinity,
              child: FilledButton.icon(
                onPressed: _isBackingUp ? null : _performWebdavBackup,
                icon: const Icon(Icons.cloud_upload),
                label: Text(_isBackingUp ? '备份中...' : '备份到WebDAV'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建WebDAV备份列表区域
  Widget _buildWebdavBackupsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.cloud, color: Theme.of(context).primaryColor),
            const SizedBox(width: 8),
            Text(
              'WebDAV备份文件',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (_webdavBackups.isEmpty)
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Center(
                child: Text(
                  '暂无WebDAV备份文件',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
            ),
          )
        else
          ...(_webdavBackups.map((fileName) => Card(
                child: ListTile(
                  leading: const Icon(Icons.file_present),
                  title: Text(fileName),
                  subtitle: Text(_formatFileName(fileName)),
                  trailing: PopupMenuButton<String>(
                    onSelected: (action) {
                      switch (action) {
                        case 'restore':
                          _restoreFromWebdav(fileName);
                          break;
                        case 'delete':
                          _deleteWebdavBackup(fileName);
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'restore',
                        child: ListTile(
                          leading: Icon(Icons.restore),
                          title: Text('恢复'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: ListTile(
                          leading: Icon(Icons.delete),
                          title: Text('删除'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                  ),
                ),
              ))),
      ],
    );
  }

  /// 构建历史记录区域
  Widget _buildHistorySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.history, color: Theme.of(context).primaryColor),
            const SizedBox(width: 8),
            Text(
              '备份历史',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (_backupHistory.isEmpty)
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Center(
                child: Text(
                  '暂无备份历史',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
            ),
          )
        else
          ...(_backupHistory.map((history) => Card(
                child: ListTile(
                  leading: Icon(
                    history.status == BackupStatus.success
                        ? Icons.check_circle
                        : Icons.error,
                    color: history.status == BackupStatus.success
                        ? Colors.green
                        : Colors.red,
                  ),
                  title: Text(history.fileName),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(_formatDateTime(history.createdAt)),
                      if (history.errorMessage != null)
                        Text(
                          history.errorMessage!,
                          style: TextStyle(color: Colors.red),
                        ),
                    ],
                  ),
                  trailing: Text(
                    history.type == BackupType.webdav ? 'WebDAV' : '本地',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
              ))),
      ],
    );
  }

  /// 格式化文件名显示
  String _formatFileName(String fileName) {
    // 从文件名中提取日期时间信息
    final regex = RegExp(r'password_backup_(\d{4}-\d{2}-\d{2})_(\d+)\.json');
    final match = regex.firstMatch(fileName);

    if (match != null) {
      final date = match.group(1);
      final timestamp = int.tryParse(match.group(2) ?? '');

      if (timestamp != null) {
        final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
        return '$date ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      }

      return date ?? fileName;
    }

    return fileName;
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
