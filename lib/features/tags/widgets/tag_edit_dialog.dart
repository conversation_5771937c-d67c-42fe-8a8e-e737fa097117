import 'package:flutter/material.dart';
import '../../../shared/widgets/color_picker_widget.dart';

/// 标签编辑对话框
class TagEditDialog extends StatefulWidget {
  final String? initialName;
  final String? initialColor;
  final String title;
  final String confirmText;

  const TagEditDialog({
    super.key,
    this.initialName,
    this.initialColor,
    this.title = '编辑标签',
    this.confirmText = '确定',
  });

  @override
  State<TagEditDialog> createState() => _TagEditDialogState();
}

class _TagEditDialogState extends State<TagEditDialog> {
  late TextEditingController nameController;
  late String selectedColor;

  // 预定义的颜色选项
  static const List<String> defaultColors = [
    '#FF6B6B', // 红色
    '#4ECDC4', // 青色
    '#45B7D1', // 蓝色
    '#96CEB4', // 绿色
    '#FFEAA7', // 黄色
    '#DDA0DD', // 紫色
    '#98D8C8', // 薄荷绿
    '#F7DC6F', // 金黄色
    '#BB8FCE', // 淡紫色
    '#85C1E9', // 天蓝色
    '#F8C471', // 橙色
    '#82E0AA', // 浅绿色
  ];

  @override
  void initState() {
    super.initState();
    nameController = TextEditingController(text: widget.initialName ?? '');
    selectedColor = widget.initialColor ?? defaultColors.first;
  }

  @override
  void dispose() {
    nameController.dispose();
    super.dispose();
  }

  void _showColorPicker() async {
    final color = await showColorPickerBottomSheet(
      context,
      initialColor: selectedColor,
      customColors: defaultColors,
    );

    if (color != null) {
      setState(() {
        selectedColor = color;
      });
    }
  }

  void _handleConfirm() {
    final name = nameController.text.trim();
    if (name.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入标签名称')),
      );
      return;
    }

    Navigator.of(context).pop({
      'name': name,
      'color': selectedColor,
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    widget.title,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // 标签名称输入
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: '标签名称',
                  hintText: '请输入标签名称',
                  border: OutlineInputBorder(),
                ),
                autofocus: true,
                textInputAction: TextInputAction.done,
                onSubmitted: (_) => _handleConfirm(),
              ),

              const SizedBox(height: 16),

              // 颜色选择
              const Text(
                '标签颜色',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),

              // 当前选择的颜色和颜色选择按钮
              Row(
                children: [
                  // 颜色预览
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: _hexToColor(selectedColor),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // 颜色值显示
                  Expanded(
                    child: Text(
                      selectedColor,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ),

                  // 选择颜色按钮
                  TextButton.icon(
                    onPressed: _showColorPicker,
                    icon: const Icon(Icons.palette, size: 18),
                    label: const Text('选择'),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // 快速颜色选择
              const Text(
                '快速选择',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),

              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: defaultColors.take(8).map((color) {
                  final isSelected = color == selectedColor;
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        selectedColor = color;
                      });
                    },
                    child: Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: _hexToColor(color),
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(
                          color:
                              isSelected ? Colors.black : Colors.grey.shade300,
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: isSelected
                          ? const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 16,
                            )
                          : null,
                    ),
                  );
                }).toList(),
              ),

              const SizedBox(height: 24),

              // 操作按钮
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('取消'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _handleConfirm,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _hexToColor(selectedColor),
                        foregroundColor: Colors.white,
                      ),
                      child: Text(widget.confirmText),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _hexToColor(String hex) {
    hex = hex.replaceAll('#', '');
    if (hex.length == 6) {
      hex = 'FF$hex';
    }
    return Color(int.parse(hex, radix: 16));
  }
}

/// 显示标签编辑底部弹窗
Future<Map<String, String>?> showTagEditDialog(
  BuildContext context, {
  String? initialName,
  String? initialColor,
  String title = '编辑标签',
  String confirmText = '确定',
}) {
  return showModalBottomSheet<Map<String, String>>(
    context: context,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
    ),
    builder: (context) => TagEditDialog(
      initialName: initialName,
      initialColor: initialColor,
      title: title,
      confirmText: confirmText,
    ),
  );
}

/// 显示添加标签对话框
Future<Map<String, String>?> showAddTagDialog(BuildContext context) {
  return showTagEditDialog(
    context,
    title: '添加标签',
    confirmText: '添加',
  );
}

/// 显示编辑标签对话框
Future<Map<String, String>?> showEditTagDialog(
  BuildContext context, {
  required String initialName,
  required String initialColor,
}) {
  return showTagEditDialog(
    context,
    initialName: initialName,
    initialColor: initialColor,
    title: '编辑标签',
    confirmText: '保存',
  );
}
