import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import '../../../../core/di/injection.dart';
import '../../../../shared/models/tag_model.dart';
import 'tag_manage_store.dart';
import '../widgets/tag_edit_dialog.dart';

class TagManagePage extends StatefulWidget {
  const TagManagePage({super.key});

  @override
  State<TagManagePage> createState() => _TagManagePageState();
}

class _TagManagePageState extends State<TagManagePage> {
  late final TagManageStore store;

  @override
  void initState() {
    super.initState();
    store = getIt<TagManageStore>();
    _initializeStore();
  }

  Future<void> _initializeStore() async {
    await store.initialize();
  }

  void _showAddTagDialog() async {
    final result = await showAddTagDialog(context);

    if (result != null) {
      final name = result['name']!;
      final color = result['color']!;
      await store.addTagWithColor(name, color);
    }
  }

  void _showEditTagDialog(TagModel tag) async {
    final result = await showEditTagDialog(
      context,
      initialName: tag.name,
      initialColor: tag.color,
    );

    if (result != null) {
      final name = result['name']!;
      final color = result['color']!;
      final updatedTag = tag.copyWith(name: name, color: color);
      await store.updateTag(updatedTag);
    }
  }

  void _showDeleteTagDialog(TagModel tag) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除标签'),
        content: Text('确定要删除标签"${tag.name}"吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await store.deleteTag(tag.id);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  void _onReorder(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }

    final reorderedTags = List<TagModel>.from(store.tags);
    final item = reorderedTags.removeAt(oldIndex);
    reorderedTags.insert(newIndex, item);

    store.reorderTags(reorderedTags);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.surfaceContainer,
        title: const Text('标签管理'),
        actions: [
          TextButton(
            child: Text(
              '添加',
              style: Theme.of(context).textTheme.titleMedium!.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                  ),
            ),
            onPressed: _showAddTagDialog,
          ),
        ],
      ),
      body: Observer(
        builder: (context) {
          if (store.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (store.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    store.errorMessage!,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.red,
                        ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => store.clearError(),
                    child: const Text('重试'),
                  ),
                ],
              ),
            );
          }

          final tags = store.tags.toList();

          if (tags.isEmpty) {
            return _buildEmptyState();
          }

          return ReorderableListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: tags.length,
            onReorder: _onReorder,
            itemBuilder: (context, index) {
              final tag = tags[index];
              return _buildTagItem(tag, key: ValueKey(tag.id));
            },
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.label_outline,
              size: 48,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              '还没有标签',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.grey.shade600,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              '点击右上角按钮添加标签',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey.shade500,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTagItem(TagModel tag, {required Key key}) {
    return Container(
      key: key,
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: ListTile(
        leading: InkWell(
          onTap: () => _showDeleteTagDialog(tag),
          child: Icon(
            Icons.remove_circle,
            color: Theme.of(context).colorScheme.error,
          ),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _getTagColor(tag.color).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: _getTagColor(tag.color).withOpacity(0.3),
                ),
              ),
              child: Text(
                tag.name,
                style: TextStyle(
                  color: _getTagColor(tag.color),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        trailing: const Icon(Icons.drag_indicator),
        onTap: () => _showEditTagDialog(tag),
      ),
    );
  }

  Color _getTagColor(String colorHex) {
    try {
      return Color(int.parse(colorHex.replaceFirst('#', '0xFF')));
    } catch (e) {
      return const Color(0xFF4286EA);
    }
  }
}
