import 'dart:convert';
import 'package:injectable/injectable.dart';
import 'package:mobx/mobx.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../shared/models/tag_model.dart';
import '../../../../shared/utils/toast_utils.dart';

part 'tag_manage_store.g.dart';

/// 标签管理状态管理
@singleton
class TagManageStore = _TagManageStore with _$TagManageStore;

abstract class _TagManageStore with Store {
  static const String _tagsKey = 'tags';

  @observable
  ObservableList<TagModel> tags = ObservableList<TagModel>();

  @observable
  bool isLoading = false;

  @observable
  String? errorMessage;

  /// 初始化标签数据
  @action
  Future<void> initialize() async {
    isLoading = true;
    errorMessage = null;

    try {
      await _loadTags();
    } catch (e) {
      errorMessage = '加载标签失败: $e';
    } finally {
      isLoading = false;
    }
  }

  /// 从本地存储加载标签
  @action
  Future<void> _loadTags() async {
    final prefs = await SharedPreferences.getInstance();
    final tagsJson = prefs.getStringList(_tagsKey) ?? [];

    final loadedTags = tagsJson
        .map((tagJson) => TagModel.fromJson(jsonDecode(tagJson)))
        .toList();

    // 按使用次数和创建时间排序
    loadedTags.sort((a, b) {
      if (a.usageCount != b.usageCount) {
        return b.usageCount.compareTo(a.usageCount); // 使用次数降序
      }
      return b.createdAt.compareTo(a.createdAt); // 创建时间降序
    });

    tags.clear();
    tags.addAll(loadedTags);
  }

  /// 保存标签到本地存储
  @action
  Future<void> _saveTags() async {
    final prefs = await SharedPreferences.getInstance();
    final tagsJson = tags.map((tag) => jsonEncode(tag.toJson())).toList();
    await prefs.setStringList(_tagsKey, tagsJson);
  }

  /// 添加标签
  @action
  Future<bool> addTag(String name, {String? color}) async {
    try {
      // 检查标签是否已存在
      if (tags.any((tag) => tag.name.toLowerCase() == name.toLowerCase())) {
        ToastUtils.showWarning('标签已存在');
        return false;
      }

      final now = DateTime.now();
      final tag = TagModel(
        id: now.millisecondsSinceEpoch.toString(),
        name: name.trim(),
        color: color ?? _getRandomColor(),
        createdAt: now,
        updatedAt: now,
      );

      tags.add(tag);
      await _saveTags();
      ToastUtils.showSuccess('标签添加成功');
      return true;
    } catch (e) {
      errorMessage = '添加标签失败: $e';
      ToastUtils.showError('添加标签失败');
      return false;
    }
  }

  /// 添加带颜色的标签
  @action
  Future<bool> addTagWithColor(String name, String color) async {
    return await addTag(name, color: color);
  }

  /// 更新标签
  @action
  Future<bool> updateTag(TagModel tag) async {
    try {
      final index = tags.indexWhere((t) => t.id == tag.id);
      if (index != -1) {
        tags[index] = tag;
        await _saveTags();
        ToastUtils.showSuccess('标签更新成功');
        return true;
      }
      return false;
    } catch (e) {
      errorMessage = '更新标签失败: $e';
      ToastUtils.showError('更新标签失败');
      return false;
    }
  }

  /// 删除标签
  @action
  Future<bool> deleteTag(String tagId) async {
    try {
      tags.removeWhere((tag) => tag.id == tagId);
      await _saveTags();
      ToastUtils.showSuccess('标签删除成功');
      return true;
    } catch (e) {
      errorMessage = '删除标签失败: $e';
      ToastUtils.showError('删除标签失败');
      return false;
    }
  }

  /// 增加标签使用次数
  @action
  Future<void> incrementTagUsage(String tagName) async {
    try {
      final index = tags.indexWhere((tag) => tag.name == tagName);
      if (index != -1) {
        tags[index] = tags[index].incrementUsage();
        await _saveTags();
      }
    } catch (e) {
      // 静默处理错误，不影响主要功能
    }
  }

  /// 批量增加标签使用次数
  @action
  Future<void> incrementTagsUsage(List<String> tagNames) async {
    for (final tagName in tagNames) {
      await incrementTagUsage(tagName);
    }
  }

  /// 根据名称获取标签
  TagModel? getTagByName(String name) {
    try {
      return tags.firstWhere((tag) => tag.name == name);
    } catch (e) {
      return null;
    }
  }

  /// 搜索标签
  List<TagModel> searchTags(String query) {
    if (query.isEmpty) return tags.toList();

    final lowerQuery = query.toLowerCase();
    return tags
        .where((tag) => tag.name.toLowerCase().contains(lowerQuery))
        .toList();
  }

  /// 获取随机颜色
  String _getRandomColor() {
    final colors = [
      '#FF6B6B',
      '#4ECDC4',
      '#45B7D1',
      '#96CEB4',
      '#FFEAA7',
      '#DDA0DD',
      '#98D8C8',
      '#F7DC6F',
      '#BB8FCE',
      '#85C1E9',
    ];
    return colors[DateTime.now().millisecond % colors.length];
  }

  /// 重新排序标签
  @action
  Future<bool> reorderTags(List<TagModel> reorderedTags) async {
    try {
      tags.clear();
      tags.addAll(reorderedTags);
      await _saveTags();
      return true;
    } catch (e) {
      errorMessage = '更新标签排序失败: $e';
      return false;
    }
  }

  /// 清除错误信息
  @action
  void clearError() {
    errorMessage = null;
  }
}
