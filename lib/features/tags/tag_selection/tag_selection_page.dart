import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/di/injection.dart';
import '../../../../shared/models/tag_model.dart';
import 'tag_selection_store.dart';
import '../widgets/tag_edit_dialog.dart';

class TagSelectionPage extends StatefulWidget {
  final List<String> initialSelectedTags;

  const TagSelectionPage({
    super.key,
    this.initialSelectedTags = const [],
  });

  @override
  State<TagSelectionPage> createState() => _TagSelectionPageState();
}

class _TagSelectionPageState extends State<TagSelectionPage> {
  late final TagSelectionStore store;

  @override
  void initState() {
    super.initState();
    store = getIt<TagSelectionStore>();
    _initializeStore();
  }

  Future<void> _initializeStore() async {
    await store.initialize(widget.initialSelectedTags);
  }

  @override
  void dispose() {
    store.dispose();
    super.dispose();
  }

  void _handleConfirm() {
    final selectedTags = store.getSelectedTags();
    context.pop(selectedTags);
  }

  void _showAddTagDialog() async {
    final result = await showAddTagDialog(context);

    if (result != null) {
      final name = result['name']!;
      final color = result['color']!;
      await store.quickAddTag(name, color: color);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('选择标签'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'add':
                  _showAddTagDialog();
                  break;
                case 'manage':
                  context.pushNamed('tag-management');
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'add',
                child: Row(
                  children: [
                    Icon(Icons.add, size: 16),
                    SizedBox(width: 8),
                    Text('添加标签'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'manage',
                child: Row(
                  children: [
                    Icon(Icons.settings, size: 16),
                    SizedBox(width: 8),
                    Text('标签管理'),
                  ],
                ),
              ),
            ],
          ),
          Observer(
            builder: (context) {
              return TextButton(
                onPressed: _handleConfirm,
                child: Text(
                  '确定${store.selectedTags.isNotEmpty ? '(${store.selectedTags.length})' : ''}',
                ),
              );
            },
          ),
        ],
      ),
      body: Observer(
        builder: (context) {
          if (store.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return Column(
            children: [
              // 搜索框
              _buildSearchBar(),

              // 已选择的标签
              if (store.selectedTags.isNotEmpty) _buildSelectedTags(),

              // 标签列表
              Expanded(
                child: _buildTagList(),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: store.searchController,
        onChanged: store.onSearchChanged,
        decoration: InputDecoration(
          hintText: '搜索标签或输入新标签',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: Observer(
            builder: (context) {
              if (store.searchQuery.isNotEmpty &&
                  store.filteredTags.isEmpty &&
                  !store.selectedTags.contains(store.searchQuery)) {
                return IconButton(
                  icon: const Icon(Icons.add),
                  onPressed: () async {
                    final result = await showAddTagDialog(context);
                    if (result != null) {
                      final name = result['name']!;
                      final color = result['color']!;
                      await store.quickAddTag(name, color: color);
                    }
                  },
                  tooltip: '添加新标签',
                );
              }
              return const SizedBox.shrink();
            },
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.blue),
          ),
        ),
      ),
    );
  }

  Widget _buildSelectedTags() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '已选择',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Observer(
            builder: (context) {
              return Wrap(
                spacing: 8,
                runSpacing: 8,
                children: store.selectedTags.map((tagName) {
                  final tag = store.allTags.firstWhere(
                    (t) => t.name == tagName,
                    orElse: () => TagModel(
                      id: '',
                      name: tagName,
                      color: '#4ECDC4',
                      createdAt: DateTime.now(),
                      updatedAt: DateTime.now(),
                    ),
                  );
                  return _buildSelectedTagChip(tag);
                }).toList(),
              );
            },
          ),
          const SizedBox(height: 16),
          const Divider(),
        ],
      ),
    );
  }

  Widget _buildSelectedTagChip(TagModel tag) {
    return Chip(
      label: Text(
        tag.name,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
      backgroundColor: _hexToColor(tag.color),
      deleteIcon: const Icon(
        Icons.close,
        size: 16,
        color: Colors.white,
      ),
      onDeleted: () => store.unselectTag(tag.name),
    );
  }

  Widget _buildTagList() {
    return Observer(
      builder: (context) {
        final tags = store.filteredTags;

        if (tags.isEmpty && store.searchQuery.isNotEmpty) {
          return _buildEmptySearchResult();
        }

        return ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // 所有标签
            Text(
              store.searchQuery.isEmpty ? '所有标签' : '搜索结果',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: tags.map(_buildTagChip).toList(),
            ),
          ],
        );
      },
    );
  }

  Widget _buildTagChip(TagModel tag) {
    return Observer(
      builder: (context) {
        final isSelected = store.selectedTags.contains(tag.name);
        return FilterChip(
          label: Text(
            tag.name,
            style: TextStyle(
              color: isSelected ? Colors.white : Colors.black87,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          selected: isSelected,
          onSelected: (_) => store.toggleTag(tag.name),
          backgroundColor: Colors.grey.shade100,
          selectedColor: _hexToColor(tag.color),
          checkmarkColor: Colors.white,
          side: BorderSide(
            color: isSelected ? _hexToColor(tag.color) : Colors.grey.shade300,
          ),
        );
      },
    );
  }

  Widget _buildEmptySearchResult() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            '没有找到相关标签',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Observer(
            builder: (context) {
              if (store.searchQuery.isNotEmpty &&
                  !store.selectedTags.contains(store.searchQuery)) {
                return ElevatedButton.icon(
                  onPressed: () async {
                    final result = await showAddTagDialog(context);
                    if (result != null) {
                      final name = result['name']!;
                      final color = result['color']!;
                      await store.quickAddTag(name, color: color);
                    }
                  },
                  icon: const Icon(Icons.add),
                  label: Text('添加 "${store.searchQuery}"'),
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }

  Color _hexToColor(String hex) {
    hex = hex.replaceAll('#', '');
    if (hex.length == 6) {
      hex = 'FF$hex';
    }
    return Color(int.parse(hex, radix: 16));
  }
}
