import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:mobx/mobx.dart';
import '../../../../core/di/injection.dart';
import '../../../../shared/models/tag_model.dart';
import '../tag_manage/tag_manage_store.dart';

part 'tag_selection_store.g.dart';

@injectable
class TagSelectionStore = _TagSelectionStore with _$TagSelectionStore;

abstract class _TagSelectionStore with Store {
  final TagManageStore _tagStore = getIt<TagManageStore>();
  final TextEditingController searchController = TextEditingController();
  final TextEditingController newTagController = TextEditingController();

  @observable
  ObservableList<String> selectedTags = ObservableList<String>();

  @observable
  String searchQuery = '';

  @observable
  bool isAddingNewTag = false;

  @observable
  bool isLoading = false;

  @observable
  String? errorMessage;

  /// 初始化
  @action
  Future<void> initialize(List<String> initialSelectedTags) async {
    isLoading = true;
    errorMessage = null;

    try {
      selectedTags.clear();
      selectedTags.addAll(initialSelectedTags);

      // 确保标签存储已初始化
      if (_tagStore.tags.isEmpty) {
        await _tagStore.initialize();
      }
    } catch (e) {
      errorMessage = '初始化失败: $e';
    } finally {
      isLoading = false;
    }
  }

  /// 获取所有标签
  @computed
  List<TagModel> get allTags => _tagStore.tags.toList();

  /// 获取过滤后的标签
  @computed
  List<TagModel> get filteredTags {
    if (searchQuery.isEmpty) {
      return allTags;
    }
    return _tagStore.searchTags(searchQuery);
  }

  /// 是否可以添加新标签
  @computed
  bool get canAddNewTag {
    final tagName = newTagController.text.trim();
    return tagName.isNotEmpty &&
        !allTags
            .any((tag) => tag.name.toLowerCase() == tagName.toLowerCase()) &&
        !selectedTags.contains(tagName);
  }

  /// 搜索变化
  @action
  void onSearchChanged(String value) {
    searchQuery = value;
  }

  /// 切换标签选择状态
  @action
  void toggleTag(String tagName) {
    if (selectedTags.contains(tagName)) {
      selectedTags.remove(tagName);
    } else {
      selectedTags.add(tagName);
    }
  }

  /// 选择标签
  @action
  void selectTag(String tagName) {
    if (!selectedTags.contains(tagName)) {
      selectedTags.add(tagName);
    }
  }

  /// 取消选择标签
  @action
  void unselectTag(String tagName) {
    selectedTags.remove(tagName);
  }

  /// 清空选择
  @action
  void clearSelection() {
    selectedTags.clear();
  }

  /// 显示添加新标签输入框
  @action
  void showAddNewTag() {
    isAddingNewTag = true;
    newTagController.clear();
  }

  /// 隐藏添加新标签输入框
  @action
  void hideAddNewTag() {
    isAddingNewTag = false;
    newTagController.clear();
  }

  /// 添加新标签
  @action
  Future<bool> addNewTag() async {
    final tagName = newTagController.text.trim();
    if (tagName.isEmpty) return false;

    try {
      final success = await _tagStore.addTag(tagName);
      if (success) {
        selectedTags.add(tagName);
        hideAddNewTag();
        return true;
      }
      return false;
    } catch (e) {
      errorMessage = '添加标签失败: $e';
      return false;
    }
  }

  /// 快速添加标签（从搜索结果）
  @action
  Future<void> quickAddTag(String tagName, {String? color}) async {
    if (tagName.trim().isEmpty) return;

    // 检查标签是否已存在
    final existingTag = _tagStore.getTagByName(tagName);
    if (existingTag == null) {
      // 标签不存在，创建新标签
      await _tagStore.addTag(tagName, color: color);
    }

    // 选择标签
    selectTag(tagName);
  }

  /// 获取选择结果
  List<String> getSelectedTags() {
    return selectedTags.toList();
  }

  /// 清除错误信息
  @action
  void clearError() {
    errorMessage = null;
  }

  /// 清理资源
  void dispose() {
    searchController.dispose();
    newTagController.dispose();
  }
}
