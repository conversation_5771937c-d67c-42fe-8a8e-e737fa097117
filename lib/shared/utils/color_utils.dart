import 'package:flutter/material.dart';

class ColorUtils {
  /// 将十六进制颜色字符串转换为Color对象
  static Color hexToColor(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }

  /// 将Color对象转换为十六进制颜色字符串
  static String colorToHex(Color color) {
    return '#${color.value.toRadixString(16).substring(2).toUpperCase()}';
  }

  /// 判断颜色是否为深色
  static bool isDarkColor(Color color) {
    final luminance = color.computeLuminance();
    return luminance < 0.5;
  }

  /// 获取颜色的对比文本颜色（黑色或白色）
  static Color getContrastTextColor(Color backgroundColor) {
    return isDarkColor(backgroundColor) ? Colors.white : Colors.black;
  }

  /// 获取颜色的浅色版本
  static Color getLightColor(Color color, {double opacity = 0.1}) {
    return color.withOpacity(opacity);
  }

  /// 获取颜色的深色版本
  static Color getDarkColor(Color color, {double factor = 0.2}) {
    final hsl = HSLColor.fromColor(color);
    return hsl.withLightness((hsl.lightness - factor).clamp(0.0, 1.0)).toColor();
  }
}
