import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

/// Toast工具类
class ToastUtils {
  /// 显示成功Toast
  static void showSuccess(String message) {
    SmartDialog.showToast(message, alignment: Alignment.center);
  }

  /// 显示错误Toast
  static void showError(String message) {
    SmartDialog.showToast(message, alignment: Alignment.center);
  }

  /// 显示警告Toast
  static void showWarning(String message) {
    SmartDialog.showToast(message, alignment: Alignment.center);
  }

  /// 显示信息Toast
  static void showInfo(String message) {
    SmartDialog.showToast(message, alignment: Alignment.center);
  }

  /// 显示加载Toast
  static void showLoading(String message) {
    SmartDialog.showLoading(msg: message);
  }
}
