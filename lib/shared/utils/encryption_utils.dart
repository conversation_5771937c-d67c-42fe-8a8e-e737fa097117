import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';

/// 加密工具类
/// 提供密码加密、解密和生成功能
class EncryptionUtils {
  /// 生成随机密码
  /// [length] 密码长度，默认16位
  /// [includeSpecialChars] 是否包含特殊字符，默认true
  static String generateRandomPassword({
    int length = 16,
    bool includeSpecialChars = true,
  }) {
    final random = Random.secure();
    String chars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';

    if (includeSpecialChars) {
      chars += '!@#\$%^&*()_+-=[]{}|;:,.<>?';
    }

    return List.generate(length, (index) => chars[random.nextInt(chars.length)])
        .join();
  }

  /// 使用SHA-256对密码进行哈希
  /// [password] 原始密码
  /// [salt] 盐值，如果为空则生成随机盐值
  /// 返回包含盐值和哈希值的Map
  static Map<String, String> hashPassword(String password, [String? salt]) {
    salt ??= _generateSalt();

    final bytes = utf8.encode(password + salt);
    final digest = sha256.convert(bytes);

    return {
      'hash': digest.toString(),
      'salt': salt,
    };
  }

  /// 验证密码
  /// [password] 输入的密码
  /// [storedHash] 存储的哈希值
  /// [salt] 盐值
  static bool verifyPassword(String password, String storedHash, String salt) {
    final result = hashPassword(password, salt);
    return result['hash'] == storedHash;
  }

  /// 生成随机盐值
  static String _generateSalt() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64.encode(bytes);
  }

  /// 简单的XOR加密（用于演示，实际项目中应使用更强的加密算法）
  /// [data] 要加密的数据
  /// [key] 加密密钥
  static String encrypt(String data, String key) {
    final dataBytes = utf8.encode(data);
    final keyBytes = utf8.encode(key);
    final encrypted = <int>[];

    for (int i = 0; i < dataBytes.length; i++) {
      encrypted.add(dataBytes[i] ^ keyBytes[i % keyBytes.length]);
    }

    return base64.encode(encrypted);
  }

  /// 简单的XOR解密
  /// [encryptedData] 加密的数据
  /// [key] 解密密钥
  static String decrypt(String encryptedData, String key) {
    final encryptedBytes = base64.decode(encryptedData);
    final keyBytes = utf8.encode(key);
    final decrypted = <int>[];

    for (int i = 0; i < encryptedBytes.length; i++) {
      decrypted.add(encryptedBytes[i] ^ keyBytes[i % keyBytes.length]);
    }

    return utf8.decode(decrypted);
  }

  /// 生成用于显示的格式化密码（添加空格分隔）
  /// [password] 原始密码
  /// [groupSize] 每组字符数，默认4
  static String formatPasswordForDisplay(String password, {int groupSize = 4}) {
    if (password.isEmpty) return '';

    final buffer = StringBuffer();
    for (int i = 0; i < password.length; i += groupSize) {
      if (i > 0) buffer.write(' ');
      final end =
          (i + groupSize < password.length) ? i + groupSize : password.length;
      buffer.write(password.substring(i, end));
    }

    return buffer.toString();
  }

  /// 检查密码强度
  /// 返回强度等级：0-弱，1-中等，2-强
  static int checkPasswordStrength(String password) {
    if (password.length < 6) return 0;

    int score = 0;

    // 长度检查
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;

    // 字符类型检查
    if (password.contains(RegExp(r'[a-z]'))) score++;
    if (password.contains(RegExp(r'[A-Z]'))) score++;
    if (password.contains(RegExp(r'[0-9]'))) score++;
    if (password.contains(RegExp(r'[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]'))) score++;

    if (score <= 2) return 0; // 弱
    if (score <= 4) return 1; // 中等
    return 2; // 强
  }

  /// 获取密码强度描述
  static String getPasswordStrengthText(int strength) {
    switch (strength) {
      case 0:
        return '弱';
      case 1:
        return '中等';
      case 2:
        return '强';
      default:
        return '未知';
    }
  }
}
