/// WebDAV配置模型
class WebdavConfig {
  final String serverUrl;
  final String username;
  final String password;
  final String directory;
  final bool isEnabled;

  const WebdavConfig({
    required this.serverUrl,
    required this.username,
    required this.password,
    this.directory = 'password_backup',
    this.isEnabled = true,
  });

  /// 从JSON创建实例
  factory WebdavConfig.fromJson(Map<String, dynamic> json) {
    return WebdavConfig(
      serverUrl: json['serverUrl'] ?? '',
      username: json['username'] ?? '',
      password: json['password'] ?? '',
      directory: json['directory'] ?? 'password_backup',
      isEnabled: json['isEnabled'] ?? true,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'serverUrl': serverUrl,
      'username': username,
      'password': password,
      'directory': directory,
      'isEnabled': isEnabled,
    };
  }

  /// 创建副本
  WebdavConfig copyWith({
    String? serverUrl,
    String? username,
    String? password,
    String? directory,
    bool? isEnabled,
  }) {
    return WebdavConfig(
      serverUrl: serverUrl ?? this.serverUrl,
      username: username ?? this.username,
      password: password ?? this.password,
      directory: directory ?? this.directory,
      isEnabled: isEnabled ?? this.isEnabled,
    );
  }

  /// 验证配置是否有效
  bool get isValid {
    return serverUrl.isNotEmpty && 
           username.isNotEmpty && 
           password.isNotEmpty &&
           (serverUrl.startsWith('http://') || serverUrl.startsWith('https://'));
  }

  @override
  String toString() {
    return 'WebdavConfig(serverUrl: $serverUrl, username: $username, directory: $directory, isEnabled: $isEnabled)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WebdavConfig &&
        other.serverUrl == serverUrl &&
        other.username == username &&
        other.password == password &&
        other.directory == directory &&
        other.isEnabled == isEnabled;
  }

  @override
  int get hashCode {
    return Object.hash(serverUrl, username, password, directory, isEnabled);
  }
}
