/// 用户模型
class UserModel {
  final String id;
  final String name;
  final String? email;
  final String? avatar; // 头像URL或本地路径
  final bool isVip;
  final DateTime? vipExpireDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UserModel({
    required this.id,
    required this.name,
    this.email,
    this.avatar,
    this.isVip = false,
    this.vipExpireDate,
    required this.createdAt,
    required this.updatedAt,
  });

  /// 从JSON创建实例
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String?,
      avatar: json['avatar'] as String?,
      isVip: json['isVip'] as bool? ?? false,
      vipExpireDate: json['vipExpireDate'] != null
          ? DateTime.parse(json['vipExpireDate'] as String)
          : null,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'avatar': avatar,
      'isVip': isVip,
      'vipExpireDate': vipExpireDate?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// 复制并修改
  UserModel copyWith({
    String? id,
    String? name,
    String? email,
    String? avatar,
    bool? isVip,
    DateTime? vipExpireDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      avatar: avatar ?? this.avatar,
      isVip: isVip ?? this.isVip,
      vipExpireDate: vipExpireDate ?? this.vipExpireDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// 检查VIP是否有效
  bool get isVipValid {
    if (!isVip) return false;
    if (vipExpireDate == null) return true; // 永久VIP
    return DateTime.now().isBefore(vipExpireDate!);
  }

  /// 获取VIP剩余天数
  int? get vipRemainingDays {
    if (!isVip || vipExpireDate == null) return null;
    final now = DateTime.now();
    if (now.isAfter(vipExpireDate!)) return 0;
    return vipExpireDate!.difference(now).inDays;
  }

  /// 获取显示名称
  String get displayName {
    return name.isNotEmpty ? name : '用户';
  }

  /// 获取头像显示文字（用于默认头像）
  String get avatarText {
    if (name.isEmpty) return '用';
    return name.substring(0, 1).toUpperCase();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserModel(id: $id, name: $name, email: $email, isVip: $isVip)';
  }
}
