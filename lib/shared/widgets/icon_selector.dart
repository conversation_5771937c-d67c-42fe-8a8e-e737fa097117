import 'package:flutter/material.dart';

class IconSelector extends StatelessWidget {
  final String selectedIcon;
  final ValueChanged<String> onIconSelected;

  const IconSelector({
    super.key,
    required this.selectedIcon,
    required this.onIconSelected,
  });

  static const List<Map<String, dynamic>> _availableIcons = [
    {'name': 'category', 'icon': Icons.category},
    {'name': 'work', 'icon': Icons.work},
    {'name': 'email', 'icon': Icons.email},
    {'name': 'shopping_cart', 'icon': Icons.shopping_cart},
    {'name': 'games', 'icon': Icons.games},
    {'name': 'music_note', 'icon': Icons.music_note},
    {'name': 'movie', 'icon': Icons.movie},
    {'name': 'sports', 'icon': Icons.sports},
    {'name': 'school', 'icon': Icons.school},
    {'name': 'local_hospital', 'icon': Icons.local_hospital},
    {'name': 'restaurant', 'icon': Icons.restaurant},
    {'name': 'directions_car', 'icon': Icons.directions_car},
    {'name': 'home', 'icon': Icons.home},
    {'name': 'account_balance', 'icon': Icons.account_balance},
    {'name': 'credit_card', 'icon': Icons.credit_card},
    {'name': 'security', 'icon': Icons.security},
    {'name': 'cloud', 'icon': Icons.cloud},
    {'name': 'phone', 'icon': Icons.phone},
    {'name': 'computer', 'icon': Icons.computer},
    {'name': 'wifi', 'icon': Icons.wifi},
    {'name': 'vpn_key', 'icon': Icons.vpn_key},
    {'name': 'lock', 'icon': Icons.lock},
    {'name': 'folder', 'icon': Icons.folder},
    {'name': 'star', 'icon': Icons.star},
    {'name': 'favorite', 'icon': Icons.favorite},
    {'name': 'bookmark', 'icon': Icons.bookmark},
    {'name': 'label', 'icon': Icons.label},
    {'name': 'more_horiz', 'icon': Icons.more_horiz},
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // 当前选中的图标预览
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getIconData(selectedIcon),
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  '当前选中: ${_getIconDisplayName(selectedIcon)}',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          // 图标网格
          Container(
            height: 200,
            padding: const EdgeInsets.all(8),
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 7,
                childAspectRatio: 1,
                crossAxisSpacing: 4,
                mainAxisSpacing: 4,
              ),
              itemCount: _availableIcons.length,
              itemBuilder: (context, index) {
                final iconData = _availableIcons[index];
                final iconName = iconData['name'] as String;
                final icon = iconData['icon'] as IconData;
                final isSelected = iconName == selectedIcon;

                return GestureDetector(
                  onTap: () => onIconSelected(iconName),
                  child: Container(
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Theme.of(context).primaryColor.withOpacity(0.1)
                          : Colors.transparent,
                      border: Border.all(
                        color: isSelected
                            ? Theme.of(context).primaryColor
                            : Colors.transparent,
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      icon,
                      size: 20,
                      color: isSelected
                          ? Theme.of(context).primaryColor
                          : Colors.grey.shade600,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  IconData _getIconData(String iconName) {
    final iconData = _availableIcons.firstWhere(
      (icon) => icon['name'] == iconName,
      orElse: () => _availableIcons.first,
    );
    return iconData['icon'] as IconData;
  }

  String _getIconDisplayName(String iconName) {
    switch (iconName) {
      case 'category':
        return '分类';
      case 'work':
        return '工作';
      case 'email':
        return '邮箱';
      case 'shopping_cart':
        return '购物';
      case 'games':
        return '游戏';
      case 'music_note':
        return '音乐';
      case 'movie':
        return '影视';
      case 'sports':
        return '运动';
      case 'school':
        return '教育';
      case 'local_hospital':
        return '医疗';
      case 'restaurant':
        return '餐饮';
      case 'directions_car':
        return '交通';
      case 'home':
        return '家庭';
      case 'account_balance':
        return '银行';
      case 'credit_card':
        return '信用卡';
      case 'security':
        return '安全';
      case 'cloud':
        return '云服务';
      case 'phone':
        return '电话';
      case 'computer':
        return '电脑';
      case 'wifi':
        return '网络';
      case 'vpn_key':
        return '密钥';
      case 'lock':
        return '锁定';
      case 'folder':
        return '文件夹';
      case 'star':
        return '星标';
      case 'favorite':
        return '收藏';
      case 'bookmark':
        return '书签';
      case 'label':
        return '标签';
      case 'more_horiz':
        return '其他';
      default:
        return iconName;
    }
  }
}
