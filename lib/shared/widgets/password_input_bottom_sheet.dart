import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../features/password/services/password_generator_service.dart';
import '../utils/toast_utils.dart';
import 'password_generator_bottom_sheet.dart';

/// 密码输入底部弹窗
class PasswordInputBottomSheet extends StatefulWidget {
  final String? initialValue;
  final String? hintText;

  const PasswordInputBottomSheet({
    super.key,
    this.initialValue,
    this.hintText,
  });

  @override
  State<PasswordInputBottomSheet> createState() => _PasswordInputBottomSheetState();
}

class _PasswordInputBottomSheetState extends State<PasswordInputBottomSheet> {
  late final TextEditingController _controller;
  bool _isPasswordVisible = false;
  PasswordStrength _passwordStrength = PasswordStrength.veryWeak;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue ?? '');
    _updatePasswordStrength();
    _controller.addListener(_updatePasswordStrength);
  }

  @override
  void dispose() {
    _controller.removeListener(_updatePasswordStrength);
    _controller.dispose();
    super.dispose();
  }

  void _updatePasswordStrength() {
    setState(() {
      _passwordStrength = PasswordGeneratorService.evaluateStrength(_controller.text);
    });
  }

  void _handleSubmit() {
    final value = _controller.text;
    Navigator.of(context).pop(value.isEmpty ? null : value);
  }

  void _handleCancel() {
    Navigator.of(context).pop();
  }

  void _togglePasswordVisibility() {
    setState(() {
      _isPasswordVisible = !_isPasswordVisible;
    });
  }

  void _copyPassword() {
    if (_controller.text.isNotEmpty) {
      Clipboard.setData(ClipboardData(text: _controller.text));
      ToastUtils.showSuccess('密码已复制到剪贴板');
    }
  }

  Future<void> _showPasswordGenerator() async {
    final generatedPassword = await showPasswordGeneratorBottomSheet(context);
    if (generatedPassword != null) {
      _controller.text = generatedPassword;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 拖拽指示器
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // 标题栏
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                TextButton(
                  onPressed: _handleCancel,
                  child: const Text('取消'),
                ),
                const Expanded(
                  child: Text(
                    '密码',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                TextButton(
                  onPressed: _handleSubmit,
                  child: const Text('确定'),
                ),
              ],
            ),
          ),
          
          const Divider(height: 1),
          
          // 输入区域
          Padding(
            padding: EdgeInsets.only(
              left: 16,
              right: 16,
              top: 16,
              bottom: MediaQuery.of(context).viewInsets.bottom + 16,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 密码输入框
                TextFormField(
                  controller: _controller,
                  obscureText: !_isPasswordVisible,
                  autofocus: true,
                  decoration: InputDecoration(
                    hintText: widget.hintText ?? '请输入密码',
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.lock_outline),
                    suffixIcon: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          onPressed: _togglePasswordVisibility,
                          icon: Icon(
                            _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                          ),
                          tooltip: _isPasswordVisible ? '隐藏密码' : '显示密码',
                        ),
                        IconButton(
                          onPressed: _copyPassword,
                          icon: const Icon(Icons.copy),
                          tooltip: '复制密码',
                        ),
                      ],
                    ),
                  ),
                  onFieldSubmitted: (_) => _handleSubmit(),
                ),
                
                const SizedBox(height: 12),
                
                // 密码强度指示器
                if (_controller.text.isNotEmpty) ...[
                  Row(
                    children: [
                      const Text('密码强度：'),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: _passwordStrength.color.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: _passwordStrength.color),
                        ),
                        child: Text(
                          _passwordStrength.displayName,
                          style: TextStyle(
                            color: _passwordStrength.color,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                ],
                
                // 操作按钮
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: _showPasswordGenerator,
                        icon: const Icon(Icons.auto_awesome),
                        label: const Text('生成密码'),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 显示密码输入底部弹窗
Future<String?> showPasswordInputBottomSheet(
  BuildContext context, {
  String? initialValue,
  String? hintText,
}) {
  return showModalBottomSheet<String>(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => PasswordInputBottomSheet(
      initialValue: initialValue,
      hintText: hintText,
    ),
  );
}
