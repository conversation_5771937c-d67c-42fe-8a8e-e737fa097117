import 'package:flutter/material.dart';

/// 简单的输入对话框结果类
class InputResult<T> {
  final T? data;
  final bool isSuccess;

  const InputResult.success(this.data) : isSuccess = true;

  const InputResult.cancelled()
      : data = null,
        isSuccess = false;

  void onSuccess(void Function(T?) callback) {
    if (isSuccess) {
      callback(data);
    }
  }
}

/// 简单的底部输入对话框
class SimpleBottomInputDialog extends StatefulWidget {
  final String title;
  final String? hintText;
  final String? initialValue;
  final TextInputType keyboardType;
  final bool isMultiline;
  final int? maxLines;

  const SimpleBottomInputDialog({
    super.key,
    required this.title,
    this.hintText,
    this.initialValue,
    this.keyboardType = TextInputType.text,
    this.isMultiline = false,
    this.maxLines,
  });

  @override
  State<SimpleBottomInputDialog> createState() =>
      _SimpleBottomInputDialogState();
}

class _SimpleBottomInputDialogState extends State<SimpleBottomInputDialog> {
  late final TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue ?? '');
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleSubmit() {
    final value = _controller.text.trim();
    Navigator.of(context)
        .pop(InputResult.success(value.isEmpty ? null : value));
  }

  void _handleCancel() {
    Navigator.of(context).pop(InputResult.cancelled());
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 拖拽指示器
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // 标题栏
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                TextButton(
                  onPressed: _handleCancel,
                  child: const Text('取消'),
                ),
                Expanded(
                  child: Text(
                    widget.title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                TextButton(
                  onPressed: _handleSubmit,
                  child: const Text('确定'),
                ),
              ],
            ),
          ),

          const Divider(height: 1),

          // 输入区域
          Padding(
            padding: EdgeInsets.only(
              left: 16,
              right: 16,
              top: 16,
              bottom: MediaQuery.of(context).viewInsets.bottom + 16,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextFormField(
                  controller: _controller,
                  keyboardType: widget.keyboardType,
                  maxLines: widget.isMultiline ? (widget.maxLines ?? 3) : 1,
                  autofocus: true,
                  decoration: InputDecoration(
                    hintText: widget.hintText,
                    border: const OutlineInputBorder(),
                  ),
                  onFieldSubmitted: (_) => _handleSubmit(),
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 显示分类选择对话框（简化版本）
Future<dynamic> showCategorySelectionDialog(
  BuildContext context, {
  required List<dynamic> categories,
  String? selectedCategoryId,
}) async {
  return showDialog<dynamic>(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('选择分类'),
      content: SizedBox(
        width: double.maxFinite,
        child: ListView.builder(
          shrinkWrap: true,
          itemCount: categories.length,
          itemBuilder: (context, index) {
            final category = categories[index];
            final isSelected = category.id == selectedCategoryId;

            return ListTile(
              title: Text(category.name),
              trailing: isSelected ? const Icon(Icons.check) : null,
              onTap: () => Navigator.of(context).pop(category),
            );
          },
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
      ],
    ),
  );
}
