import 'package:flutter/material.dart';

/// 颜色选择器组件
class ColorPickerWidget extends StatefulWidget {
  final String? initialColor;
  final ValueChanged<String> onColorSelected;
  final List<String>? customColors;

  const ColorPickerWidget({
    super.key,
    this.initialColor,
    required this.onColorSelected,
    this.customColors,
  });

  @override
  State<ColorPickerWidget> createState() => _ColorPickerWidgetState();
}

class _ColorPickerWidgetState extends State<ColorPickerWidget> {
  late String selectedColor;

  // 预定义的颜色选项
  static const List<String> defaultColors = [
    '#FF6B6B', // 红色
    '#4ECDC4', // 青色
    '#45B7D1', // 蓝色
    '#96CEB4', // 绿色
    '#FFEAA7', // 黄色
    '#DDA0DD', // 紫色
    '#98D8C8', // 薄荷绿
    '#F7DC6F', // 金黄色
    '#BB8FCE', // 淡紫色
    '#85C1E9', // 天蓝色
    '#F8C471', // 橙色
    '#82E0AA', // 浅绿色
    '#F1948A', // 粉红色
    '#85929E', // 灰色
    '#D7BDE2', // 薰衣草色
    '#A9DFBF', // 浅薄荷绿
    '#F9E79F', // 浅黄色
    '#AED6F1', // 浅蓝色
  ];

  @override
  void initState() {
    super.initState();
    selectedColor = widget.initialColor ?? defaultColors.first;
  }

  @override
  Widget build(BuildContext context) {
    final colors = widget.customColors ?? defaultColors;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '选择颜色',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // 当前选择的颜色预览
          Container(
            width: double.infinity,
            height: 60,
            decoration: BoxDecoration(
              color: _hexToColor(selectedColor),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Center(
              child: Text(
                selectedColor,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      offset: Offset(1, 1),
                      blurRadius: 2,
                      color: Colors.black26,
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // 颜色选择网格
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 6,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 1,
            ),
            itemCount: colors.length,
            itemBuilder: (context, index) {
              final color = colors[index];
              final isSelected = color == selectedColor;
              
              return GestureDetector(
                onTap: () {
                  setState(() {
                    selectedColor = color;
                  });
                  widget.onColorSelected(color);
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: _hexToColor(color),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isSelected ? Colors.black : Colors.grey.shade300,
                      width: isSelected ? 3 : 1,
                    ),
                  ),
                  child: isSelected
                      ? const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 20,
                        )
                      : null,
                ),
              );
            },
          ),
          
          const SizedBox(height: 16),
          
          // 确认按钮
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                widget.onColorSelected(selectedColor);
                Navigator.of(context).pop(selectedColor);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: _hexToColor(selectedColor),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                '确定',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _hexToColor(String hex) {
    hex = hex.replaceAll('#', '');
    if (hex.length == 6) {
      hex = 'FF$hex';
    }
    return Color(int.parse(hex, radix: 16));
  }
}

/// 显示颜色选择器对话框
Future<String?> showColorPickerDialog(
  BuildContext context, {
  String? initialColor,
  List<String>? customColors,
}) {
  return showDialog<String>(
    context: context,
    builder: (context) => AlertDialog(
      contentPadding: EdgeInsets.zero,
      content: ColorPickerWidget(
        initialColor: initialColor,
        customColors: customColors,
        onColorSelected: (color) {
          // 实时更新，但不关闭对话框
        },
      ),
    ),
  );
}

/// 显示颜色选择器底部弹窗
Future<String?> showColorPickerBottomSheet(
  BuildContext context, {
  String? initialColor,
  List<String>? customColors,
}) {
  return showModalBottomSheet<String>(
    context: context,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
    ),
    builder: (context) => ColorPickerWidget(
      initialColor: initialColor,
      customColors: customColors,
      onColorSelected: (color) {
        // 实时更新，但不关闭底部弹窗
      },
    ),
  );
}
