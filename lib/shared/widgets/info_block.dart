import 'package:flutter/material.dart';

/// 信息块组件
class InfoBlock extends StatelessWidget {
  final IconData icon;
  final String label;
  final String? value;
  final String? placeholder;
  final bool isRequired;
  final bool isPassword;
  final VoidCallback? onTap;
  final Widget? trailing;

  const InfoBlock({
    super.key,
    required this.icon,
    required this.label,
    this.value,
    this.placeholder,
    this.isRequired = false,
    this.isPassword = false,
    this.onTap,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // 图标
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
              ),
              
              const SizedBox(width: 12),
              
              // 标签和值
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          label,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        if (isRequired) ...[
                          const SizedBox(width: 4),
                          const Text(
                            '*',
                            style: TextStyle(
                              color: Colors.red,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      value ?? placeholder ?? '',
                      style: TextStyle(
                        fontSize: 16,
                        color: value != null 
                            ? Theme.of(context).textTheme.bodyLarge?.color
                            : Colors.grey.shade600,
                        fontFamily: isPassword ? 'monospace' : null,
                      ),
                    ),
                  ],
                ),
              ),
              
              // 尾部组件
              if (trailing != null) ...[
                const SizedBox(width: 8),
                trailing!,
              ],
              
              // 箭头图标
              if (onTap != null) ...[
                const SizedBox(width: 8),
                Icon(
                  Icons.chevron_right,
                  color: Colors.grey.shade400,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
