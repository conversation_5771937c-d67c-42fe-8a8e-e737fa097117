import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../features/password/services/password_generator_service.dart';
import '../utils/toast_utils.dart';

/// 密码生成器底部弹窗
class PasswordGeneratorBottomSheet extends StatefulWidget {
  const PasswordGeneratorBottomSheet({super.key});

  @override
  State<PasswordGeneratorBottomSheet> createState() => _PasswordGeneratorBottomSheetState();
}

class _PasswordGeneratorBottomSheetState extends State<PasswordGeneratorBottomSheet> {
  PasswordGeneratorConfig _config = const PasswordGeneratorConfig();
  String _generatedPassword = '';
  bool _isPasswordVisible = true;

  @override
  void initState() {
    super.initState();
    _generatePassword();
  }

  void _generatePassword() {
    try {
      setState(() {
        _generatedPassword = PasswordGeneratorService.generatePassword(_config);
      });
    } catch (e) {
      ToastUtils.showError('生成密码失败：$e');
    }
  }

  void _updateConfig(PasswordGeneratorConfig newConfig) {
    setState(() {
      _config = newConfig;
    });
    _generatePassword();
  }

  void _copyPassword() {
    if (_generatedPassword.isNotEmpty) {
      Clipboard.setData(ClipboardData(text: _generatedPassword));
      ToastUtils.showSuccess('密码已复制到剪贴板');
    }
  }

  void _usePassword() {
    Navigator.of(context).pop(_generatedPassword);
  }

  void _togglePasswordVisibility() {
    setState(() {
      _isPasswordVisible = !_isPasswordVisible;
    });
  }

  PasswordStrength get _passwordStrength {
    return PasswordGeneratorService.evaluateStrength(_generatedPassword);
  }

  Color _getStrengthColor() {
    switch (_passwordStrength) {
      case PasswordStrength.veryWeak:
        return Colors.red;
      case PasswordStrength.weak:
        return Colors.orange;
      case PasswordStrength.medium:
        return Colors.yellow.shade700;
      case PasswordStrength.strong:
        return Colors.lightGreen;
      case PasswordStrength.veryStrong:
        return Colors.green;
    }
  }

  String _getStrengthText() {
    switch (_passwordStrength) {
      case PasswordStrength.veryWeak:
        return '非常弱';
      case PasswordStrength.weak:
        return '弱';
      case PasswordStrength.medium:
        return '中等';
      case PasswordStrength.strong:
        return '强';
      case PasswordStrength.veryStrong:
        return '非常强';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 拖拽指示器
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // 标题栏
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
                const Expanded(
                  child: Text(
                    '密码生成器',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                TextButton(
                  onPressed: _usePassword,
                  child: const Text('使用'),
                ),
              ],
            ),
          ),
          
          const Divider(height: 1),
          
          // 内容区域
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 生成的密码显示
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey.shade200),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: SelectableText(
                                _isPasswordVisible ? _generatedPassword : '●' * _generatedPassword.length,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontFamily: 'monospace',
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            IconButton(
                              onPressed: _togglePasswordVisibility,
                              icon: Icon(
                                _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                              ),
                              tooltip: _isPasswordVisible ? '隐藏密码' : '显示密码',
                            ),
                            IconButton(
                              onPressed: _copyPassword,
                              icon: const Icon(Icons.copy),
                              tooltip: '复制密码',
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            const Text('强度：'),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: _getStrengthColor().withOpacity(0.2),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(color: _getStrengthColor()),
                              ),
                              child: Text(
                                _getStrengthText(),
                                style: TextStyle(
                                  color: _getStrengthColor(),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            const Spacer(),
                            TextButton.icon(
                              onPressed: _generatePassword,
                              icon: const Icon(Icons.refresh, size: 16),
                              label: const Text('重新生成'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // 密码长度设置
                  const Text(
                    '密码长度',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: Slider(
                          value: _config.length.toDouble(),
                          min: 4,
                          max: 32,
                          divisions: 28,
                          label: _config.length.toString(),
                          onChanged: (value) {
                            _updateConfig(_config.copyWith(length: value.round()));
                          },
                        ),
                      ),
                      Container(
                        width: 40,
                        alignment: Alignment.center,
                        child: Text(
                          _config.length.toString(),
                          style: const TextStyle(fontWeight: FontWeight.w600),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 字符类型选择
                  const Text(
                    '包含字符类型',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 8),
                  
                  CheckboxListTile(
                    title: const Text('大写字母 (A-Z)'),
                    value: _config.includeUppercase,
                    onChanged: (value) {
                      _updateConfig(_config.copyWith(includeUppercase: value));
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                  
                  CheckboxListTile(
                    title: const Text('小写字母 (a-z)'),
                    value: _config.includeLowercase,
                    onChanged: (value) {
                      _updateConfig(_config.copyWith(includeLowercase: value));
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                  
                  CheckboxListTile(
                    title: const Text('数字 (0-9)'),
                    value: _config.includeNumbers,
                    onChanged: (value) {
                      _updateConfig(_config.copyWith(includeNumbers: value));
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                  
                  CheckboxListTile(
                    title: const Text('特殊符号 (!@#\$%^&*)'),
                    value: _config.includeSymbols,
                    onChanged: (value) {
                      _updateConfig(_config.copyWith(includeSymbols: value));
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 高级选项
                  const Text(
                    '高级选项',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 8),
                  
                  CheckboxListTile(
                    title: const Text('排除相似字符 (il1Lo0O)'),
                    value: _config.excludeSimilar,
                    onChanged: (value) {
                      _updateConfig(_config.copyWith(excludeSimilar: value));
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                  
                  CheckboxListTile(
                    title: const Text('排除模糊字符 ({}[]()/\\\'"`~,;.<>)'),
                    value: _config.excludeAmbiguous,
                    onChanged: (value) {
                      _updateConfig(_config.copyWith(excludeAmbiguous: value));
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                  
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 显示密码生成器底部弹窗
Future<String?> showPasswordGeneratorBottomSheet(BuildContext context) {
  return showModalBottomSheet<String>(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => SizedBox(
      height: MediaQuery.of(context).size.height * 0.8,
      child: const PasswordGeneratorBottomSheet(),
    ),
  );
}
