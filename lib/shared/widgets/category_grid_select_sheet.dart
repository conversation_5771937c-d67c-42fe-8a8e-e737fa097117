import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../shared/models/category_model.dart';
import '../../shared/utils/color_utils.dart';

class CategoryGridSelectSheet extends StatelessWidget {
  final CategoryModel? selectedCategory;
  final List<CategoryModel> categories;

  const CategoryGridSelectSheet({
    super.key,
    this.selectedCategory,
    required this.categories,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Safe<PERSON>rea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 拖拽指示器
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // 标题栏
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  Text(
                    '选择分类',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(width: 12),
                  InkWell(
                    child: Text(
                      '管理分类',
                      style: textTheme.titleMedium?.copyWith(
                        color: colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    onTap: () {
                      context.pushNamed('category-management');
                    },
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => context.pop(),
                  ),
                ],
              ),
            ),

            Divider(height: 0.5, color: Colors.grey.shade300),

            // 分类网格
            Flexible(
              child: GridView.builder(
                padding: const EdgeInsets.all(16),
                shrinkWrap: true,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 4,
                  childAspectRatio: 0.9,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                ),
                itemCount: categories.length,
                itemBuilder: (context, index) {
                  final category = categories[index];
                  final isSelected = selectedCategory?.id == category.id;
                  final categoryColor = ColorUtils.hexToColor(category.color);

                  return InkWell(
                    onTap: () => context.pop(category),
                    borderRadius: BorderRadius.circular(12),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: 56,
                          height: 56,
                          decoration: BoxDecoration(
                            color: isSelected
                                ? categoryColor.withValues(alpha: 0.2)
                                : colorScheme.surfaceContainer,
                            shape: BoxShape.circle,
                            border: isSelected
                                ? Border.all(color: categoryColor, width: 2)
                                : null,
                          ),
                          child: Center(
                            child: Icon(
                              _getCategoryIcon(category.icon),
                              size: 28,
                              color: isSelected ? categoryColor : categoryColor,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          category.name,
                          style: textTheme.bodySmall?.copyWith(
                            fontWeight: isSelected
                                ? FontWeight.w600
                                : FontWeight.normal,
                            color: isSelected ? categoryColor : null,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  /// 根据图标名称获取IconData
  IconData _getCategoryIcon(String iconName) {
    switch (iconName) {
      case 'group':
        return Icons.group;
      case 'email':
        return Icons.email;
      case 'account_balance':
        return Icons.account_balance;
      case 'shopping_cart':
        return Icons.shopping_cart;
      case 'movie':
        return Icons.movie;
      case 'work':
        return Icons.work;
      case 'school':
        return Icons.school;
      case 'flight':
        return Icons.flight;
      case 'local_hospital':
        return Icons.local_hospital;
      case 'sports_esports':
        return Icons.sports_esports;
      case 'music_note':
        return Icons.music_note;
      case 'camera_alt':
        return Icons.camera_alt;
      case 'restaurant':
        return Icons.restaurant;
      case 'home':
        return Icons.home;
      case 'car_rental':
        return Icons.car_rental;
      case 'more_horiz':
        return Icons.more_horiz;
      default:
        return Icons.category;
    }
  }
}

/// 显示分类网格选择器
Future<CategoryModel?> showCategoryGridSelect(
  BuildContext context, {
  CategoryModel? selectedCategory,
  required List<CategoryModel> categories,
}) async {
  final result = await showModalBottomSheet<CategoryModel>(
    context: context,
    backgroundColor: Colors.transparent,
    isScrollControlled: true,
    constraints: const BoxConstraints(
      minWidth: double.infinity,
      maxWidth: double.infinity,
    ),
    builder: (context) => CategoryGridSelectSheet(
      selectedCategory: selectedCategory,
      categories: categories,
    ),
  );

  return result;
}
