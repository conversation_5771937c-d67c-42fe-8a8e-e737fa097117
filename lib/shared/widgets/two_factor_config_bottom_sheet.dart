import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../services/two_factor_service.dart';
import '../../features/backup/qr_scanner/qr_scanner_utils.dart';
import '../utils/toast_utils.dart';

/// 2FA配置底部弹窗
class TwoFactorConfigBottomSheet extends StatefulWidget {
  final String? initialSecret;
  final bool initialEnabled;
  final String accountName;

  const TwoFactorConfigBottomSheet({
    super.key,
    this.initialSecret,
    this.initialEnabled = false,
    required this.accountName,
  });

  @override
  State<TwoFactorConfigBottomSheet> createState() =>
      _TwoFactorConfigBottomSheetState();
}

class _TwoFactorConfigBottomSheetState
    extends State<TwoFactorConfigBottomSheet> {
  late TextEditingController _secretController;
  late bool _enabled;
  bool _secretVisible = false;

  @override
  void initState() {
    super.initState();
    _secretController = TextEditingController(text: widget.initialSecret ?? '');
    _enabled = widget.initialEnabled;
  }

  @override
  void dispose() {
    _secretController.dispose();
    super.dispose();
  }

  bool get _isSecretValid {
    final secret = _secretController.text.trim();
    return secret.isNotEmpty && TwoFactorService.isValidSecret(secret);
  }

  void _generateSecret() {
    final newSecret = TwoFactorService.generateSecret();
    _secretController.text = newSecret;
    setState(() {});
    ToastUtils.showSuccess('已生成新的密钥');
  }

  void _copySecret() {
    final secret = _secretController.text.trim();
    if (secret.isNotEmpty) {
      Clipboard.setData(ClipboardData(text: secret));
      ToastUtils.showSuccess('密钥已复制');
    }
  }

  void _pasteSecret() async {
    final data = await Clipboard.getData('text/plain');
    if (data?.text != null) {
      final cleanedSecret = TwoFactorService.cleanSecret(data!.text!);
      _secretController.text = cleanedSecret;
      setState(() {});
    }
  }

  void _importFromQrImage() async {
    try {
      final result = await QrImportService.importFromImage();
    } catch (e) {
      ToastUtils.showError('导入失败：$e');
    }
  }

  void _importFromQrCamera() async {
    try {
      // 显示加载提示
      ToastUtils.showInfo('正在启动相机...');
      final result = await QrImportService.importFromCamera(context);
    } catch (e) {
      print('相机扫描失败: $e');
      ToastUtils.showError('扫描失败，请尝试从图片导入');
      // 自动尝试图片导入作为备选方案
      _importFromQrImage();
    }
  }

  void _showImportAlternatives() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('导入2FA密钥'),
        content: const Text('扫描失败，请选择其他导入方式：'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _importFromQrImage();
            },
            child: const Text('从图片导入'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  void _onSave() {
    final secret = _secretController.text.trim();
    Navigator.of(context).pop({
      'enabled': _enabled && _isSecretValid,
      'secret': _enabled && _isSecretValid ? secret : null,
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              const Icon(Icons.security, color: Colors.blue),
              const SizedBox(width: 8),
              const Text(
                '双因素认证 (2FA)',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // 启用开关
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                const Icon(Icons.toggle_on, color: Colors.grey),
                const SizedBox(width: 12),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '启用2FA',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        '为此账号启用双因素认证',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: _enabled,
                  onChanged: (value) {
                    setState(() {
                      _enabled = value;
                    });
                  },
                ),
              ],
            ),
          ),

          if (_enabled) ...[
            const SizedBox(height: 16),

            // 密钥输入
            const Text(
              '2FA密钥',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _isSecretValid
                      ? Colors.green.shade300
                      : Colors.grey.shade300,
                ),
              ),
              child: Column(
                children: [
                  TextField(
                    controller: _secretController,
                    obscureText: !_secretVisible,
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 14,
                    ),
                    decoration: InputDecoration(
                      hintText: '输入或生成2FA密钥',
                      border: InputBorder.none,
                      suffixIcon: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            onPressed: () {
                              setState(() {
                                _secretVisible = !_secretVisible;
                              });
                            },
                            icon: Icon(
                              _secretVisible
                                  ? Icons.visibility_off
                                  : Icons.visibility,
                              size: 20,
                            ),
                          ),
                          IconButton(
                            onPressed: _copySecret,
                            icon: const Icon(Icons.copy, size: 20),
                          ),
                          IconButton(
                            onPressed: _pasteSecret,
                            icon: const Icon(Icons.paste, size: 20),
                            tooltip: '粘贴',
                          ),
                          IconButton(
                            onPressed: _importFromQrImage,
                            icon: const Icon(Icons.photo, size: 20),
                            tooltip: '从图片导入',
                          ),
                          IconButton(
                            onPressed: _importFromQrCamera,
                            icon: const Icon(Icons.qr_code_scanner, size: 20),
                            tooltip: '扫描二维码',
                          ),
                        ],
                      ),
                    ),
                    onChanged: (value) {
                      setState(() {});
                    },
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: _generateSecret,
                          child: const Text('生成密钥'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      if (_isSecretValid)
                        const Icon(
                          Icons.check_circle,
                          color: Colors.green,
                          size: 20,
                        )
                      else if (_secretController.text.trim().isNotEmpty)
                        const Icon(
                          Icons.error,
                          color: Colors.red,
                          size: 20,
                        ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline,
                          size: 16, color: Colors.blue.shade700),
                      const SizedBox(width: 8),
                      Text(
                        '导入方式',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '• 手动输入：直接输入或粘贴2FA密钥\n'
                    '• 生成密钥：自动生成新的2FA密钥\n'
                    '• 从图片导入：选择包含2FA二维码的图片\n'
                    '• 扫描二维码：使用相机扫描2FA二维码',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),
          ],

          const SizedBox(height: 24),

          // 操作按钮
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: _enabled && !_isSecretValid ? null : _onSave,
                  child: const Text('确定'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
