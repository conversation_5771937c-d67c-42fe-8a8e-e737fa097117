import 'package:flutter/material.dart';
import 'package:ruolanui/ruolanui.dart';

class ItemSelectForm extends StatefulWidget {
  const ItemSelectForm(
      {super.key,
      required this.title,
      this.hintText,
      required this.value,
      this.required = false,
      required this.onTap,
      this.padding = const EdgeInsets.symmetric(horizontal: 12, vertical: 16)});

  final String title;

  final String? hintText;

  final String value;

  final bool required;

  final VoidCallback onTap;

  final EdgeInsetsGeometry? padding;

  @override
  State<ItemSelectForm> createState() => _ItemSelectFormState();
}

class _ItemSelectFormState extends State<ItemSelectForm> {
  int index = 0;

  TextTheme get textTheme => Theme.of(context).textTheme;

  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return InkWell(
      onTap: widget.onTap,
      child: Container(
        padding: widget.padding,
        decoration: BoxDecoration(
          color: colorScheme.surfaceContainer, // 使用相同的背景色
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              widget.title,
              style: textTheme.bodyMedium,
            ),
            Visibility(
              visible: widget.required,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4.0),
                child: Text(
                  "*",
                  style:
                      textTheme.bodyMedium!.copyWith(color: colorScheme.error),
                ),
              ),
            ),
            Spacer(),
            SizedBox(
              width: 6,
            ),
            // 区分hint和value
            ConditionalBuilder(
              condition: widget.value.isNotEmpty,
              trueBuilder: (c) => Text(
                widget.value,
                style: textTheme.titleMedium,
              ),
              falseBuilder: (c) => Text(
                widget.hintText ?? "",
                style: textTheme.bodyMedium,
              ),
            ),
            SizedBox(
              width: 6,
            ),
            Icon(
              Icons.chevron_right,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }
}
