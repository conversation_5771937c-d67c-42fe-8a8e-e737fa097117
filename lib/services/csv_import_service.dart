import 'dart:io';

import 'package:csv/csv.dart';
import 'package:file_picker/file_picker.dart';
import 'package:injectable/injectable.dart';
import 'package:password/model/account_model.dart';
import 'package:password/model/netmodel/account_net_model.dart';
import 'package:ruolanui/ruolanui.dart';

/// CSV导入服务
/// 负责处理从CSV文件导入账号数据的业务逻辑
@singleton
class CsvImportService {
  /// 从CSV文件导入账号数据
  /// 返回导入成功的账号网络模型列表，如果失败返回null
  Future<Result<List<AccountModel>>> importFromCsv() async {
    try {
      // 打开文件选择器，限制为CSV文件
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'],
        allowMultiple: false,
      );

      if (result == null || result.files.isEmpty) {
        // 用户取消选择
        return Result.failure("用户取消选择");
      }

      final file = File(result.files.single.path!);
      if (!await file.exists()) {
        return Result.failure("文件不存在");
      }

      // 读取CSV文件内容
      final csvContent = await file.readAsString();
      if (csvContent.trim().isEmpty) {
        return Result.failure("CSV文件为空");
      }

      // 解析CSV数据
      final accounts = await _parseCsvData(csvContent);

      if (accounts.isEmpty) {
        return Result.failure("CSV文件中没有有效的账号数据");
      }

      final list = accounts.map((i) => i.toAccountModel()).toList();
      return Result.success(list);
    } catch (e) {
      return Result.failure(e.toString());
    }
  }

  /// 解析CSV数据
  /// [csvContent] CSV文件内容
  /// 返回解析后的账号网络模型列表
  Future<List<AccountNetModel>> _parseCsvData(String csvContent) async {
    try {
      // 使用CSV解析器解析数据
      List<List<dynamic>> csvData =
          const CsvToListConverter().convert(csvContent);

      if (csvData.isEmpty) {
        return [];
      }

      // 获取表头
      final headers =
          csvData.first.map((e) => e.toString().toLowerCase().trim()).toList();

      // 验证必要的字段是否存在
      if (!_validateHeaders(headers)) {
        throw Exception('CSV文件缺少必要的字段（name, username, password）');
      }

      final accounts = <AccountNetModel>[];

      // 从第二行开始处理数据（跳过表头）
      for (int i = 1; i < csvData.length; i++) {
        final row = csvData[i];
        if (row.isEmpty) continue;

        try {
          final account = _mapCsvToAccount(headers, row, i);
          if (account != null) {
            accounts.add(account);
          }
        } catch (e) {
          print('解析第${i + 1}行数据失败: $e');
          // 继续处理其他行，不因为单行错误而中断整个导入过程
        }
      }

      return accounts;
    } catch (e) {
      print('解析CSV数据失败: $e');
      rethrow;
    }
  }

  /// 验证CSV表头是否包含必要字段
  /// [headers] 表头列表
  /// 返回是否包含必要字段
  bool _validateHeaders(List<String> headers) {
    final requiredFields = ['name', 'username', 'password'];
    return requiredFields.every((field) => headers.contains(field));
  }

  /// 将CSV行数据映射为AccountNetModel
  /// [headers] 表头列表
  /// [row] 数据行
  /// [rowIndex] 行索引（用于生成ID）
  /// 返回映射后的AccountNetModel，如果数据无效返回null
  AccountNetModel? _mapCsvToAccount(
      List<String> headers, List<dynamic> row, int rowIndex) {
    try {
      // 创建字段映射
      final Map<String, String> fieldMap = {};
      for (int i = 0; i < headers.length && i < row.length; i++) {
        fieldMap[headers[i]] = row[i]?.toString().trim() ?? '';
      }
      // name,url,username,password,note
      // 验证必要字段
      final name = fieldMap['name'] ?? '';
      final username = fieldMap['username'] ?? '';
      final password = fieldMap['password'] ?? '';
      final url = fieldMap['url'] ?? '';
      final note = fieldMap['note'] ?? '';

      if (name.isEmpty || username.isEmpty) {
        print('第${rowIndex + 1}行数据不完整，跳过');
        return null;
      }

      // 使用新的CSV构造方法
      return AccountNetModel.fromCsvData(
        name: name,
        username: username,
        password: password,
        website: url,
        notes: note,
      );
    } catch (e) {
      print('映射第${rowIndex + 1}行数据失败: $e');
      return null;
    }
  }

  /// 验证CSV文件格式
  /// [filePath] 文件路径
  /// 返回验证结果和错误信息
  Future<Map<String, dynamic>> validateCsvFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        return {'valid': false, 'error': '文件不存在'};
      }

      final csvContent = await file.readAsString();
      if (csvContent.trim().isEmpty) {
        return {'valid': false, 'error': 'CSV文件为空'};
      }

      List<List<dynamic>> csvData =
          const CsvToListConverter().convert(csvContent);
      if (csvData.isEmpty) {
        return {'valid': false, 'error': 'CSV文件没有数据'};
      }

      final headers =
          csvData.first.map((e) => e.toString().toLowerCase().trim()).toList();
      if (!_validateHeaders(headers)) {
        return {
          'valid': false,
          'error': 'CSV文件缺少必要的字段（name, username, password）'
        };
      }

      return {
        'valid': true,
        'rowCount': csvData.length - 1, // 减去表头行
        'headers': headers,
      };
    } catch (e) {
      return {'valid': false, 'error': '文件格式错误: $e'};
    }
  }
}
