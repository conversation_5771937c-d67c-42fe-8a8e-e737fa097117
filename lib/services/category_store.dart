import 'package:injectable/injectable.dart';
import 'package:mobx/mobx.dart';
import 'package:password/data/backup_repo.dart';
import 'package:password/shared/models/category_model.dart';

part 'category_store.g.dart';

@lazySingleton
class CategoryStore = _CategoryStore with _$CategoryStore;

abstract class _CategoryStore with Store {
  final BackupRepo _backupRepo;

  // Observable状态
  @observable
  ObservableList<CategoryModel> categories = ObservableList<CategoryModel>();

  _CategoryStore(this._backupRepo) {
    initialize();
  }

  Future<void> initialize() async {
    await _loadCategories();
  }

  /// 加载分类数据
  @action
  Future<void> _loadCategories() async {
    final cacheCategories =
        _backupRepo.getBackupDataSync()?.categoriesWithoutNull ?? List.empty();
    categories.clear();
    categories.addAll(cacheCategories.map((i) => i.toCategoryModel()));
  }
}
