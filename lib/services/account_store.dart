import 'package:injectable/injectable.dart';
import 'package:mobx/mobx.dart';
import 'package:password/data/backup_repo.dart';

import '../model/account_model.dart';
import '../shared/utils/toast_utils.dart';

part 'account_store.g.dart';

/// 账号密码状态管理
@lazySingleton
class AccountStore = _AccountStore with _$AccountStore;

abstract class _AccountStore with Store {
  final BackupRepo _backupRepo;

  _AccountStore(this._backupRepo) {
    initialize();
  }

  // Observable状态
  @observable
  ObservableList<AccountModel> accounts = ObservableList<AccountModel>();

  @observable
  bool isLoading = false;

  @observable
  String? errorMessage;

  @observable
  String? selectedCategoryId;

  /// 初始化账号数据
  @action
  Future<void> initialize() async {
    isLoading = true;
    try {
      await _loadAccounts();
    } catch (e) {
      errorMessage = '加载账号失败: $e';
    } finally {
      isLoading = false;
    }
  }

  /// 加载账号数据
  @action
  Future<void> _loadAccounts() async {
    try {
      final cacheAccounts =
          _backupRepo.getBackupDataSync()?.accountsWithoutNull ?? [];
      accounts.clear();
      accounts.addAll(cacheAccounts.map((i) => i.toAccountModel()));
    } catch (e) {
      print('加载账号数据失败: $e');
      accounts.clear();
    }
  }

  /// 保存账号到本地存储
  Future<void> _saveAccounts() async {
    await _backupRepo.saveAccountData(accounts);
  }

  /// 添加账号
  @action
  Future<bool> addAccount(AccountModel account) async {
    try {
      accounts.insert(0, account); // 插入到最前面
      await _saveAccounts();
      ToastUtils.showSuccess('账号添加成功');
      return true;
    } catch (e) {
      errorMessage = '添加账号失败: $e';
      ToastUtils.showError('添加账号失败');
      return false;
    }
  }

  /// 添加多条账号信息
  @action
  Future<bool> addAccounts(List<AccountModel> accounts) async {
    try {
      this.accounts.addAll(accounts);
      await _saveAccounts();
      ToastUtils.showSuccess('账号添加成功');
      return true;
    } catch (e) {
      errorMessage = '添加账号失败: $e';
      ToastUtils.showError('添加账号失败');
      return false;
    }
  }

  /// 更新账号
  @action
  Future<bool> updateAccount(AccountModel account) async {
    try {
      final index = accounts.indexWhere((a) => a.id == account.id);
      if (index != -1) {
        accounts[index] = account;
      }
      await _saveAccounts();
      ToastUtils.showSuccess('账号更新成功');
      return true;
    } catch (e) {
      errorMessage = '更新账号失败: $e';
      ToastUtils.showError('更新账号失败');
      return false;
    }
  }

  /// 删除账号
  @action
  Future<bool> deleteAccount(String accountId) async {
    try {
      accounts.removeWhere((a) => a.id == accountId);
      await _saveAccounts();
      ToastUtils.showSuccess('账号删除成功');
      return true;
    } catch (e) {
      errorMessage = '删除账号失败: $e';
      ToastUtils.showError('删除账号失败');
      return false;
    }
  }

  /// 切换收藏状态
  @action
  Future<bool> toggleFavorite(String accountId) async {
    try {
      final account = accounts.firstWhere((a) => a.id == accountId);
      final updatedAccount = account.copyWith(
        isFavorite: !account.isFavorite,
        updatedAt: DateTime.now(),
      );
      return await updateAccount(updatedAccount);
    } catch (e) {
      errorMessage = '更新收藏状态失败: $e';
      ToastUtils.showError('更新收藏状态失败');
      return false;
    }
  }

  /// 更新最后使用时间
  @action
  Future<bool> updateLastUsed(String accountId) async {
    try {
      final account = accounts.firstWhere((a) => a.id == accountId);
      final updatedAccount = account.copyWith(
        lastUsed: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      return await updateAccount(updatedAccount);
    } catch (e) {
      return false;
    }
  }

  /// 根据分类筛选账号
  List<AccountModel> getAccountsByCategory(String? categoryId) {
    if (categoryId == null) {
      return accounts.toList(); // 返回全部
    }
    return accounts.where((a) => a.categoryId == categoryId).toList();
  }

  /// 搜索账号
  List<AccountModel> searchAccounts(String query) {
    if (query.isEmpty) return accounts.toList();

    final lowerQuery = query.toLowerCase();
    return accounts.where((account) {
      return account.title.toLowerCase().contains(lowerQuery) ||
          account.username.toLowerCase().contains(lowerQuery) ||
          (account.email?.toLowerCase().contains(lowerQuery) ?? false) ||
          (account.website?.toLowerCase().contains(lowerQuery) ?? false) ||
          account.tags.any((tag) => tag.toLowerCase().contains(lowerQuery));
    }).toList();
  }

  /// 获取收藏的账号
  List<AccountModel> getFavoriteAccounts() {
    return accounts.where((a) => a.isFavorite).toList();
  }

  /// 获取最近使用的账号
  List<AccountModel> getRecentlyUsedAccounts({int limit = 10}) {
    final recentAccounts = accounts.where((a) => a.lastUsed != null).toList();
    recentAccounts.sort((a, b) => b.lastUsed!.compareTo(a.lastUsed!));
    return recentAccounts.take(limit).toList();
  }

  /// 根据ID获取账号
  AccountModel? getAccountById(String id) {
    try {
      return accounts.firstWhere((a) => a.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 设置选中的分类
  @action
  void setSelectedCategory(String? categoryId) {
    selectedCategoryId = categoryId;
  }

  /// 清除错误信息
  @action
  void clearError() {
    errorMessage = null;
  }
}
