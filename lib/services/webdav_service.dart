import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:injectable/injectable.dart';
import '../shared/models/webdav_config.dart';

/// 简单的WebDAV客户端服务
/// 使用HttpClient实现基本的WebDAV操作
@singleton
class WebdavService {
  /// 测试WebDAV连接
  Future<bool> testConnection(WebdavConfig config) async {
    try {
      final client = HttpClient();
      client.connectionTimeout = const Duration(seconds: 10);

      // 创建基本认证头
      final auth =
          base64Encode(utf8.encode('${config.username}:${config.password}'));

      final uri = Uri.parse(config.serverUrl);
      final request = await client.openUrl('OPTIONS', uri);
      request.headers.set('Authorization', 'Basic $auth');

      final response = await request.close();
      client.close();

      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      print('WebDAV连接测试失败: $e');
      return false;
    }
  }

  /// 创建目录
  Future<bool> createDirectory(WebdavConfig config, String path) async {
    try {
      final client = HttpClient();
      client.connectionTimeout = const Duration(seconds: 30);

      final auth =
          base64Encode(utf8.encode('${config.username}:${config.password}'));
      final uri =
          Uri.parse('${config.serverUrl.replaceAll(RegExp(r'/+$'), '')}/$path');

      final request = await client.openUrl('MKCOL', uri);
      request.headers.set('Authorization', 'Basic $auth');

      final response = await request.close();
      client.close();

      // 201 Created 或 405 Method Not Allowed (目录已存在) 都算成功
      return response.statusCode == 201 || response.statusCode == 405;
    } catch (e) {
      print('创建WebDAV目录失败: $e');
      return false;
    }
  }

  /// 上传文件
  Future<bool> uploadFile(
      WebdavConfig config, String remotePath, Uint8List data,
      {Function(int sent, int total)? onProgress}) async {
    try {
      final client = HttpClient();
      client.connectionTimeout = const Duration(seconds: 60);

      final auth =
          base64Encode(utf8.encode('${config.username}:${config.password}'));
      final uri = Uri.parse(
          '${config.serverUrl.replaceAll(RegExp(r'/+$'), '')}/$remotePath');

      final request = await client.openUrl('PUT', uri);
      request.headers.set('Authorization', 'Basic $auth');
      request.headers.set('Content-Type', 'application/octet-stream');
      request.headers.set('Content-Length', data.length.toString());

      // 写入数据
      request.add(data);

      final response = await request.close();
      client.close();

      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      print('上传文件到WebDAV失败: $e');
      return false;
    }
  }

  /// 下载文件
  Future<Uint8List?> downloadFile(WebdavConfig config, String remotePath,
      {Function(int received, int total)? onProgress}) async {
    try {
      final client = HttpClient();
      client.connectionTimeout = const Duration(seconds: 60);

      final auth =
          base64Encode(utf8.encode('${config.username}:${config.password}'));
      final uri = Uri.parse(
          '${config.serverUrl.replaceAll(RegExp(r'/+$'), '')}/$remotePath');

      final request = await client.openUrl('GET', uri);
      request.headers.set('Authorization', 'Basic $auth');

      final response = await request.close();

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final bytes = <int>[];
        await for (final chunk in response) {
          bytes.addAll(chunk);
          onProgress?.call(bytes.length, response.contentLength);
        }
        client.close();
        return Uint8List.fromList(bytes);
      }

      client.close();
      return null;
    } catch (e) {
      print('从WebDAV下载文件失败: $e');
      return null;
    }
  }

  /// 列出目录内容
  Future<List<String>?> listDirectory(WebdavConfig config, String path) async {
    try {
      final client = HttpClient();
      client.connectionTimeout = const Duration(seconds: 30);

      final auth =
          base64Encode(utf8.encode('${config.username}:${config.password}'));
      final uri =
          Uri.parse('${config.serverUrl.replaceAll(RegExp(r'/+$'), '')}/$path');

      final request = await client.openUrl('PROPFIND', uri);
      request.headers.set('Authorization', 'Basic $auth');
      request.headers.set('Depth', '1');
      request.headers.set('Content-Type', 'application/xml');

      // 简单的PROPFIND请求体
      const propfindBody = '''<?xml version="1.0" encoding="utf-8" ?>
<D:propfind xmlns:D="DAV:">
  <D:prop>
    <D:displayname/>
    <D:resourcetype/>
  </D:prop>
</D:propfind>''';

      request.write(propfindBody);

      final response = await request.close();

      if (response.statusCode == 207) {
        // Multi-Status
        final responseBody = await response.transform(utf8.decoder).join();
        client.close();

        // 简单解析XML响应（实际项目中应使用XML解析库）
        final files = <String>[];
        final displayNameRegex =
            RegExp(r'<D:displayname>(.*?)</D:displayname>');
        final matches = displayNameRegex.allMatches(responseBody);

        for (final match in matches) {
          final fileName = match.group(1);
          if (fileName != null && fileName.isNotEmpty) {
            files.add(fileName);
          }
        }

        return files;
      }

      client.close();
      return null;
    } catch (e) {
      print('列出WebDAV目录失败: $e');
      return null;
    }
  }

  /// 删除文件
  Future<bool> deleteFile(WebdavConfig config, String remotePath) async {
    try {
      final client = HttpClient();
      client.connectionTimeout = const Duration(seconds: 30);

      final auth =
          base64Encode(utf8.encode('${config.username}:${config.password}'));
      final uri = Uri.parse(
          '${config.serverUrl.replaceAll(RegExp(r'/+$'), '')}/$remotePath');

      final request = await client.openUrl('DELETE', uri);
      request.headers.set('Authorization', 'Basic $auth');

      final response = await request.close();
      client.close();

      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      print('删除WebDAV文件失败: $e');
      return false;
    }
  }
}
