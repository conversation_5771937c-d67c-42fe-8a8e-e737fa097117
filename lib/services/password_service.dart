import 'package:shared_preferences/shared_preferences.dart';
import 'package:injectable/injectable.dart';
import '../shared/utils/encryption_utils.dart';

/// 密码管理服务
/// 负责主密码的存储、验证和管理
@singleton
class PasswordService {
  static const String _masterPasswordHashKey = 'master_password_hash';
  static const String _masterPasswordSaltKey = 'master_password_salt';
  static const String _isSetupCompleteKey = 'is_setup_complete';

  /// 检查是否已经设置过主密码
  Future<bool> isSetupComplete() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isSetupCompleteKey) ?? false;
  }

  /// 设置主密码
  /// [password] 主密码
  /// 返回是否设置成功
  Future<bool> setMasterPassword(String password) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 生成哈希和盐值
      final hashResult = EncryptionUtils.hashPassword(password);

      // 保存到本地存储
      await prefs.setString(_masterPasswordHashKey, hashResult['hash']!);
      await prefs.setString(_masterPasswordSaltKey, hashResult['salt']!);
      await prefs.setBool(_isSetupCompleteKey, true);

      return true;
    } catch (e) {
      print('设置主密码失败: $e');
      return false;
    }
  }

  /// 验证主密码
  /// [password] 输入的密码
  /// 返回是否验证成功
  Future<bool> verifyMasterPassword(String password) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final storedHash = prefs.getString(_masterPasswordHashKey);
      final salt = prefs.getString(_masterPasswordSaltKey);

      if (storedHash == null || salt == null) {
        return false;
      }

      return EncryptionUtils.verifyPassword(password, storedHash, salt);
    } catch (e) {
      print('验证主密码失败: $e');
      return false;
    }
  }

  /// 重置主密码（清除所有数据）
  /// 注意：这将清除所有存储的密码数据
  Future<bool> resetMasterPassword() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 清除主密码相关数据
      await prefs.remove(_masterPasswordHashKey);
      await prefs.remove(_masterPasswordSaltKey);
      await prefs.remove(_isSetupCompleteKey);

      // 这里可以添加清除其他加密数据的逻辑

      return true;
    } catch (e) {
      print('重置主密码失败: $e');
      return false;
    }
  }

  /// 更改主密码
  /// [oldPassword] 旧密码
  /// [newPassword] 新密码
  /// 返回是否更改成功
  Future<bool> changeMasterPassword(
      String oldPassword, String newPassword) async {
    try {
      // 首先验证旧密码
      final isOldPasswordValid = await verifyMasterPassword(oldPassword);
      if (!isOldPasswordValid) {
        return false;
      }

      // 设置新密码
      return await setMasterPassword(newPassword);
    } catch (e) {
      print('更改主密码失败: $e');
      return false;
    }
  }

  /// 获取用于加密其他密码的密钥
  /// 这里简化处理，实际项目中应该使用更复杂的密钥派生
  /// [masterPassword] 主密码
  Future<String?> getEncryptionKey(String masterPassword) async {
    final isValid = await verifyMasterPassword(masterPassword);
    if (!isValid) {
      return null;
    }

    // 这里可以使用PBKDF2或其他密钥派生函数
    // 为了简化，直接使用主密码的哈希作为加密密钥
    final prefs = await SharedPreferences.getInstance();
    final salt = prefs.getString(_masterPasswordSaltKey);
    if (salt == null) return null;

    final hashResult = EncryptionUtils.hashPassword(masterPassword, salt);
    return hashResult['hash'];
  }

  /// 检查主密码强度
  /// [password] 密码
  /// 返回强度等级和描述
  Map<String, dynamic> checkPasswordStrength(String password) {
    final strength = EncryptionUtils.checkPasswordStrength(password);
    return {
      'level': strength,
      'text': EncryptionUtils.getPasswordStrengthText(strength),
    };
  }

  /// 生成建议的主密码
  /// [length] 密码长度，默认16位
  String generateSuggestedPassword({int length = 16}) {
    return EncryptionUtils.generateRandomPassword(
      length: length,
      includeSpecialChars: true,
    );
  }
}
