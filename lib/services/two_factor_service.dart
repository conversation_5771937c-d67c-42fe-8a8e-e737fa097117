import 'dart:math';
import 'dart:typed_data';
import 'package:otp/otp.dart';
import 'package:base32/base32.dart';

/// 双因素认证服务
class TwoFactorService {
  static const int _secretLength = 32; // Base32编码的密钥长度
  static const int _codeLength = 6; // 验证码长度
  static const int _timeStep = 30; // 时间步长（秒）

  /// 生成随机的2FA密钥
  /// 返回Base32编码的密钥字符串
  static String generateSecret() {
    final random = Random.secure();
    final bytes = Uint8List(_secretLength ~/ 8 * 5); // Base32需要的字节数

    for (int i = 0; i < bytes.length; i++) {
      bytes[i] = random.nextInt(256);
    }

    return base32.encode(bytes).replaceAll('=', ''); // 移除填充字符
  }

  /// 验证密钥格式是否正确
  /// [secret] Base32编码的密钥
  static bool isValidSecret(String secret) {
    if (secret.isEmpty) return false;

    try {
      // 尝试解码Base32
      base32.decode(secret);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 生成当前时间的TOTP验证码
  /// [secret] Base32编码的密钥
  /// [time] 可选的时间戳，默认使用当前时间
  static String generateTOTP(String secret, {DateTime? time}) {
    if (!isValidSecret(secret)) {
      throw ArgumentError('Invalid secret format');
    }

    final currentTime = time ?? DateTime.now();
    final timestamp = currentTime.millisecondsSinceEpoch ~/ 1000;

    return OTP.generateTOTPCodeString(
      secret,
      timestamp,
      length: _codeLength,
      interval: _timeStep,
      algorithm: Algorithm.SHA1,
      isGoogle: true,
    );
  }

  /// 验证TOTP验证码是否正确
  /// [secret] Base32编码的密钥
  /// [code] 用户输入的验证码
  /// [time] 可选的时间戳，默认使用当前时间
  /// [window] 时间窗口，允许前后几个时间步长的验证码，默认为1
  static bool verifyTOTP(String secret, String code,
      {DateTime? time, int window = 1}) {
    if (!isValidSecret(secret) || code.length != _codeLength) {
      return false;
    }

    final currentTime = time ?? DateTime.now();
    final timestamp = currentTime.millisecondsSinceEpoch ~/ 1000;

    // 检查当前时间窗口及前后窗口
    for (int i = -window; i <= window; i++) {
      final testTimestamp = timestamp + (i * _timeStep);
      final expectedCode = OTP.generateTOTPCodeString(
        secret,
        testTimestamp,
        length: _codeLength,
        interval: _timeStep,
        algorithm: Algorithm.SHA1,
        isGoogle: true,
      );

      if (expectedCode == code) {
        return true;
      }
    }

    return false;
  }

  /// 获取当前验证码的剩余有效时间（秒）
  /// 返回到下一个时间步长的剩余秒数
  static int getRemainingTime({DateTime? time}) {
    final currentTime = time ?? DateTime.now();
    final timestamp = currentTime.millisecondsSinceEpoch ~/ 1000;
    return _timeStep - (timestamp % _timeStep);
  }

  /// 生成Google Authenticator兼容的URI
  /// [secret] Base32编码的密钥
  /// [accountName] 账号名称
  /// [issuer] 发行者名称，默认为"密码管理器"
  static String generateAuthenticatorUri(
    String secret,
    String accountName, {
    String issuer = '密码管理器',
  }) {
    if (!isValidSecret(secret)) {
      throw ArgumentError('Invalid secret format');
    }

    final encodedAccountName = Uri.encodeComponent(accountName);
    final encodedIssuer = Uri.encodeComponent(issuer);

    return 'otpauth://totp/$encodedIssuer:$encodedAccountName'
        '?secret=$secret'
        '&issuer=$encodedIssuer'
        '&algorithm=SHA1'
        '&digits=$_codeLength'
        '&period=$_timeStep';
  }

  /// 从URI解析2FA信息
  /// [uri] 各种Authenticator应用格式的URI
  /// 返回包含secret、accountName、issuer的Map，解析失败返回null
  static Map<String, String>? parseAuthenticatorUri(String uri) {
    try {
      // 预处理URI，处理一些常见的编码问题
      String cleanedUri = _preprocessUri(uri);

      final parsedUri = Uri.parse(cleanedUri);

      // 支持otpauth协议的totp和hotp类型
      if (parsedUri.scheme != 'otpauth' ||
          (parsedUri.host != 'totp' && parsedUri.host != 'hotp')) {
        return null;
      }

      // 获取密钥，支持多种参数名
      String? secret = parsedUri.queryParameters['secret'] ??
          parsedUri.queryParameters['Secret'] ??
          parsedUri.queryParameters['SECRET'];

      if (secret == null) {
        return null;
      }

      // 清理密钥格式
      secret = cleanSecret(secret);

      if (!isValidSecret(secret)) {
        return null;
      }

      // 解析账号名称和发行者
      final pathInfo = _parsePathInfo(parsedUri.path);
      String? issuer = parsedUri.queryParameters['issuer'] ??
          parsedUri.queryParameters['Issuer'] ??
          parsedUri.queryParameters['ISSUER'] ??
          pathInfo['issuer'];

      String? accountName = pathInfo['accountName'];

      // 如果没有账号名称，尝试从issuer中提取
      if (accountName?.isEmpty == true && issuer?.isNotEmpty == true) {
        accountName = issuer;
        issuer = '';
      }

      return {
        'secret': secret,
        'accountName': accountName ?? '',
        'issuer': issuer ?? '',
      };
    } catch (e) {
      print('解析URI失败: $e, URI: $uri');
      return null;
    }
  }

  /// 预处理URI，修复常见的编码问题
  static String _preprocessUri(String uri) {
    String cleaned = uri.trim();

    // 处理一些常见的编码问题
    cleaned = cleaned.replaceAll(' ', '%20');

    // 处理双重编码问题
    try {
      if (cleaned.contains('%25')) {
        cleaned = Uri.decodeComponent(cleaned);
      }
    } catch (e) {
      // 如果解码失败，使用原始URI
    }

    return cleaned;
  }

  /// 解析路径信息，提取账号名称和发行者
  static Map<String, String?> _parsePathInfo(String path) {
    try {
      if (path.isEmpty || path == '/') {
        return {'accountName': null, 'issuer': null};
      }

      // 移除开头的'/'
      String cleanPath = path.startsWith('/') ? path.substring(1) : path;

      if (cleanPath.isEmpty) {
        return {'accountName': null, 'issuer': null};
      }

      // 处理不同的路径格式
      if (cleanPath.contains(':')) {
        // 格式: issuer:account 或 issuer%3Aaccount
        final parts = cleanPath.split(':');
        if (parts.length >= 2) {
          return {
            'accountName': Uri.decodeComponent(parts[1]),
            'issuer': Uri.decodeComponent(parts[0]),
          };
        }
      } else if (cleanPath.contains('%3A')) {
        // 处理URL编码的冒号
        final parts = cleanPath.split('%3A');
        if (parts.length >= 2) {
          return {
            'accountName': Uri.decodeComponent(parts[1]),
            'issuer': Uri.decodeComponent(parts[0]),
          };
        }
      }

      // 如果没有冒号分隔符，整个路径作为账号名称
      return {
        'accountName': Uri.decodeComponent(cleanPath),
        'issuer': null,
      };
    } catch (e) {
      print('解析路径失败: $e, path: $path');
      return {'accountName': null, 'issuer': null};
    }
  }

  /// 格式化密钥显示（添加空格分隔）
  /// [secret] Base32编码的密钥
  static String formatSecretForDisplay(String secret) {
    if (secret.isEmpty) return secret;

    final buffer = StringBuffer();
    for (int i = 0; i < secret.length; i += 4) {
      if (i > 0) buffer.write(' ');
      final end = (i + 4 < secret.length) ? i + 4 : secret.length;
      buffer.write(secret.substring(i, end));
    }
    return buffer.toString();
  }

  /// 清理密钥格式（移除空格和特殊字符）
  /// [secret] 用户输入的密钥
  static String cleanSecret(String secret) {
    return secret.toUpperCase().replaceAll(RegExp(r'[^A-Z2-7]'), '');
  }
}
