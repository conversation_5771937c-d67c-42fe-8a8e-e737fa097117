import 'dart:convert';
import 'dart:typed_data';
import 'package:password/data/backup_lds.dart';
import 'package:password/data/backup_repo.dart';
import 'package:password/features/category/category_manage/category_manage_store.dart';
import 'package:password/services/account_store.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:injectable/injectable.dart';
import '../shared/models/webdav_config.dart';
import '../model/netmodel/backup_net_model.dart';
import 'webdav_service.dart';

/// 备份服务
/// 负责数据的备份和恢复
@injectable
class BackupService {
  static const String _webdavConfigKey = 'webdav_config';
  static const String _backupHistoryKey = 'backup_history';

  final WebdavService _webdavService;

  final BackupRepo _backupRepo;

  final BackupLds _backupLds;

  BackupService(this._webdavService, this._backupRepo, this._backupLds);

  /// 保存WebDAV配置
  Future<bool> saveWebdavConfig(WebdavConfig config) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(config.toJson());
      await prefs.setString(_webdavConfigKey, jsonString);
      return true;
    } catch (e) {
      print('保存WebDAV配置失败: $e');
      return false;
    }
  }

  /// 获取WebDAV配置
  Future<WebdavConfig?> getWebdavConfig() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_webdavConfigKey);
      if (jsonString == null) return null;

      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      return WebdavConfig.fromJson(json);
    } catch (e) {
      print('获取WebDAV配置失败: $e');
      return null;
    }
  }

  /// 测试WebDAV连接
  Future<bool> testWebdavConnection(WebdavConfig config) async {
    return await _webdavService.testConnection(config);
  }

  /// 备份到WebDAV
  Future<void> backupToWebdav({
    Function(String message)? onProgress,
    Function(int sent, int total)? onUploadProgress,
  }) async {
    try {
      onProgress?.call('正在获取WebDAV配置...');

      final config = await getWebdavConfig();
      if (config == null || !config.isValid) {
        throw Exception('WebDAV配置无效，请先配置WebDAV服务');
      }

      onProgress?.call('正在准备备份数据...');

      final jsonString = _backupLds.getBackupData();
      if (jsonString == null) {
        return;
      }
      final data = Uint8List.fromList(utf8.encode(jsonString));

      onProgress?.call('正在创建远程目录...');

      // 确保远程目录存在
      await _webdavService.createDirectory(config, config.directory);

      onProgress?.call('正在上传备份文件...');

      // 上传备份文件
      final remotePath = '${config.directory}/backup.json';
      final success = await _webdavService.uploadFile(
        config,
        remotePath,
        data,
        onProgress: onUploadProgress,
      );

      if (!success) {
        throw Exception('上传备份文件失败');
      }

      onProgress?.call('正在保存备份记录...');
    } catch (e) {
      print('WebDAV备份失败: $e');
    }
  }

  /// 从WebDAV恢复
  Future<bool> restoreFromWebdav(
    String fileName, {
    Function(String message)? onProgress,
    Function(int received, int total)? onDownloadProgress,
  }) async {
    try {
      onProgress?.call('正在获取WebDAV配置...');

      final config = await getWebdavConfig();
      if (config == null || !config.isValid) {
        throw Exception('WebDAV配置无效，请先配置WebDAV服务');
      }

      onProgress?.call('正在下载备份文件...');

      // 下载备份文件
      final remotePath = '${config.directory}/$fileName';
      final data = await _webdavService.downloadFile(
        config,
        remotePath,
        onProgress: onDownloadProgress,
      );

      if (data == null) {
        throw Exception('下载备份文件失败');
      }

      onProgress?.call('正在解析备份数据...');

      // 解析备份数据
      final jsonString = utf8.decode(data);
      final backupData = BackupNetModel.fromJsonString(jsonString);

      onProgress?.call('正在恢复数据...');

      // 恢复数据
      await _restoreData(backupData);

      onProgress?.call('恢复完成');

      return true;
    } catch (e) {
      print('WebDAV恢复失败: $e');
      rethrow;
    }
  }

  /// 恢复数据到本地存储
  Future<void> _restoreData(BackupNetModel backupData) async {
    try {
      // 由于依赖注入问题，暂时只打印恢复信息
      // 实际项目中应该恢复到AccountStore和CategoryStore
      // print(
      //     '恢复数据: ${backupData.accounts.length} 个账号, ${backupData.categories.length} 个分类');
      // TODO: 实现实际的数据恢复逻辑
    } catch (e) {
      print('恢复数据失败: $e');
      rethrow;
    }
  }

  /// 获取WebDAV备份文件列表
  Future<List<String>> getWebdavBackupList() async {
    try {
      final config = await getWebdavConfig();
      if (config == null || !config.isValid) {
        return [];
      }

      final files =
          await _webdavService.listDirectory(config, config.directory);
      if (files == null) return [];

      // 过滤出备份文件
      return files
          .where((file) =>
              file.startsWith('password_backup_') && file.endsWith('.json'))
          .toList();
    } catch (e) {
      print('获取WebDAV备份列表失败: $e');
      return [];
    }
  }

  /// 删除WebDAV备份文件
  Future<bool> deleteWebdavBackup(String fileName) async {
    try {
      final config = await getWebdavConfig();
      if (config == null || !config.isValid) {
        return false;
      }

      final remotePath = '${config.directory}/$fileName';
      return await _webdavService.deleteFile(config, remotePath);
    } catch (e) {
      print('删除WebDAV备份失败: $e');
      return false;
    }
  }

  /// 保存备份历史
  Future<void> _saveBackupHistory(BackupHistory history) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyList = await getBackupHistory();
      historyList.insert(0, history);

      // 只保留最近50条记录
      if (historyList.length > 50) {
        historyList.removeRange(50, historyList.length);
      }

      final jsonList = historyList.map((h) => jsonEncode(h.toJson())).toList();
      await prefs.setStringList(_backupHistoryKey, jsonList);
    } catch (e) {
      print('保存备份历史失败: $e');
    }
  }

  /// 获取备份历史
  Future<List<BackupHistory>> getBackupHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = prefs.getStringList(_backupHistoryKey) ?? [];

      return jsonList.map((jsonString) {
        final json = jsonDecode(jsonString) as Map<String, dynamic>;
        return BackupHistory.fromJson(json);
      }).toList();
    } catch (e) {
      print('获取备份历史失败: $e');
      return [];
    }
  }
}
